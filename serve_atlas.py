#!/usr/bin/env python3
"""
Simple HTTP server to serve the A.T.L.A.S. interface
This bypasses CORS issues by serving from localhost
"""

import http.server
import socketserver
import webbrowser
import threading
import time
import os
import sys

class AtlasHTTPServer:
    def __init__(self, port=8080):
        self.port = port
        self.server = None
        
    def start_server(self):
        """Start the HTTP server"""
        try:
            handler = http.server.SimpleHTTPRequestHandler
            
            # Add CORS headers to allow API calls
            class CORSRequestHandler(handler):
                def end_headers(self):
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    super().end_headers()
            
            with socketserver.TCPServer(("", self.port), CORSRequestHandler) as httpd:
                self.server = httpd
                print(f"🌐 A.T.L.A.S. Interface Server running at http://localhost:{self.port}")
                print(f"📁 Serving from: {os.getcwd()}")
                httpd.serve_forever()
                
        except OSError as e:
            if "Address already in use" in str(e):
                print(f"❌ Port {self.port} is already in use. Trying port {self.port + 1}...")
                self.port += 1
                self.start_server()
            else:
                print(f"❌ Error starting server: {e}")
                return False
        except KeyboardInterrupt:
            print("\n👋 Server stopped by user")
            return True
    
    def open_browser(self):
        """Open the browser after a short delay"""
        time.sleep(2)  # Wait for server to start
        url = f"http://localhost:{self.port}/atlas_interface.html"
        print(f"🚀 Opening A.T.L.A.S. interface: {url}")
        webbrowser.open(url)

def main():
    print("=" * 70)
    print("🚀 A.T.L.A.S. LEE METHOD SCANNER - WEB SERVER")
    print("=" * 70)
    print("Advanced Trading & Learning Analysis System")
    print("Serving interface via HTTP to bypass CORS restrictions")
    print("=" * 70)
    
    # Check if the HTML file exists
    if not os.path.exists("atlas_interface.html"):
        print("❌ atlas_interface.html not found in current directory")
        print(f"📁 Current directory: {os.getcwd()}")
        input("Press Enter to exit...")
        return
    
    # Create and start server
    server = AtlasHTTPServer()
    
    # Start browser in a separate thread
    browser_thread = threading.Thread(target=server.open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    print("\n💡 Features enabled:")
    print("   • No CORS restrictions")
    print("   • Full Lee Method API access")
    print("   • Real-time scanner integration")
    print("   • Professional web interface")
    print("\n🔗 API Server should be running at http://localhost:5001")
    print("📊 Interface will be available at http://localhost:8080")
    print("\nPress Ctrl+C to stop the server...")
    
    # Start the server (this blocks)
    server.start_server()

if __name__ == "__main__":
    main()
