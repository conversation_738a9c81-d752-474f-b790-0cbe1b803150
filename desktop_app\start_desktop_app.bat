@echo off
echo ======================================================================
echo 🚀 A.T.L.A.S. LEE METHOD SCANNER - DESKTOP APP LAUNCHER
echo ======================================================================
echo Advanced Trading ^& Learning Analysis System
echo Starting Professional Desktop Application
echo ======================================================================

echo [STEP 1] Installing required Python package...
pip install pywebview
if %errorlevel% neq 0 (
    echo ❌ Failed to install pywebview
    echo 💡 Make sure Python is installed and pip is available
    pause
    exit /b 1
)
echo ✅ pywebview installed

echo.
echo [STEP 2] Starting A.T.L.A.S. Desktop Application...
python launch_desktop_app.py

echo.
echo ======================================================================
echo 👋 A.T.L.A.S. Desktop Application Closed
echo ======================================================================
pause
