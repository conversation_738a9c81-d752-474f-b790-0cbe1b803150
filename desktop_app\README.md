# A.T.L.A.S. Desktop Application

This folder contains all files for the A.T.L.A.S. desktop application built with Electron.

## Files:
- `main.js` - Electron main process
- `launch_desktop_app.py` - Python desktop launcher
- `package.json` - Node.js dependencies and scripts
- `package-lock.json` - Node.js dependency lock file
- `install_desktop_app.bat` - Windows installation script
- `start_desktop_app.bat` - Windows startup script
- `node_modules/` - Node.js dependencies

## Usage:

### Option 1: Electron App
```bash
cd desktop_app
npm start
```

### Option 2: Python Launcher
```bash
cd desktop_app
python launch_desktop_app.py
```

### Option 3: Windows Batch
```bash
cd desktop_app
start_desktop_app.bat
```

## Requirements:
- Node.js and npm (for Electron)
- Python 3.9+ with webview package (for Python launcher)
