#!/usr/bin/env python3
"""
Enhanced <PERSON> Method Scanner for A.T.L.A.S.
Implements Lee Method pattern detection replacing TTM Squeeze patterns
"""

import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import requests
import json

# Configure logging with ASCII-safe format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('lee_method_scanner.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class LeeMethodSignal:
    """Enhanced Lee Method signal replacing TTM Squeeze"""
    symbol: str
    signal_type: str  # 'bullish_momentum', 'bearish_momentum', 'neutral'
    entry_price: float
    target_price: float
    stop_loss: float
    confidence: float
    timeframe: str
    timestamp: datetime

    # Lee Method specific data
    histogram_sequence: List[float]  # The decreasing + increasing sequence
    momentum_bars: List[float]  # Momentum values for confirmation
    momentum_confirmation: bool
    trend_alignment: bool

    # Multi-timeframe analysis
    weekly_trend: str  # 'bullish', 'bearish', 'neutral'
    daily_trend: str

    # Risk metrics
    risk_reward_ratio: float
    position_size_percent: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'signal_type': self.signal_type,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'confidence': self.confidence,
            'timeframe': self.timeframe,
            'timestamp': self.timestamp.isoformat(),
            'histogram_bars': self.histogram_bars,
            'momentum_confirmation': self.momentum_confirmation,
            'volume_confirmation': self.volume_confirmation,
            'ema_alignment': self.ema_alignment,
            'weekly_trend': self.weekly_trend,
            'daily_trend': self.daily_trend,
            'risk_reward_ratio': self.risk_reward_ratio,
            'position_size_percent': self.position_size_percent
        }

class EnhancedLeeMethodScanner:
    """Enhanced Lee Method scanner replacing TTM Squeeze with Lee Method patterns"""

    def __init__(self, fmp_api_key: str = None):
        self.logger = logger
        self.fmp_api_key = fmp_api_key or "demo"  # Use demo key for testing
        self.base_url = "https://financialmodelingprep.com/api/v3"

        # Lee Method parameters
        self.momentum_period = 12
        self.min_decreasing_bars = 3  # Minimum 3 decreasing bars
        self.max_lookback_bars = 10   # Maximum bars to look back for pattern

        # Multi-timeframe parameters
        self.ema_periods = [5, 8, 21, 50]

        # Risk management
        self.default_risk_percent = 2.0  # 2% risk per trade
        self.min_volume_ratio = 1.2  # 20% above average
        
    async def fetch_historical_data(self, symbol: str, timeframe: str = "1day", limit: int = 100) -> pd.DataFrame:
        """Fetch real historical data from FMP API"""
        try:
            if timeframe == "1day":
                endpoint = f"{self.base_url}/historical-price-full/{symbol}"
                params = {"apikey": self.fmp_api_key}
            else:
                # For intraday data
                endpoint = f"{self.base_url}/historical-chart/{timeframe}/{symbol}"
                params = {"apikey": self.fmp_api_key}
            
            response = requests.get(endpoint, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if timeframe == "1day" and "historical" in data:
                    df = pd.DataFrame(data["historical"])
                elif isinstance(data, list):
                    df = pd.DataFrame(data)
                else:
                    self.logger.warning(f"Unexpected data format for {symbol}")
                    return pd.DataFrame()
                
                if df.empty:
                    return df
                
                # Standardize column names
                df.columns = df.columns.str.lower()
                
                # Convert date column
                if 'date' in df.columns:
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                
                # Ensure numeric columns
                numeric_cols = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_cols:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Sort by date (oldest first)
                df.sort_index(inplace=True)
                
                # Take most recent data
                df = df.tail(limit)
                
                self.logger.info(f"Fetched {len(df)} bars for {symbol} ({timeframe})")
                return df
                
            else:
                self.logger.error(f"API error for {symbol}: {response.status_code}")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_ttm_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate TTM Squeeze indicators with enhanced features"""
        if df.empty or len(df) < max(self.bb_period, self.kc_period):
            return df
        
        try:
            # Bollinger Bands
            df['sma'] = df['close'].rolling(window=self.bb_period).mean()
            df['bb_std'] = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['sma'] + (self.bb_stddev * df['bb_std'])
            df['bb_lower'] = df['sma'] - (self.bb_stddev * df['bb_std'])
            
            # True Range and ATR for Keltner Channels
            df['prev_close'] = df['close'].shift(1)
            df['tr1'] = df['high'] - df['low']
            df['tr2'] = (df['high'] - df['prev_close']).abs()
            df['tr3'] = (df['low'] - df['prev_close']).abs()
            df['true_range'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
            df['atr'] = df['true_range'].rolling(window=self.kc_period).mean()
            
            # Keltner Channels
            df['kc_middle'] = df['close'].rolling(window=self.kc_period).mean()
            df['kc_upper'] = df['kc_middle'] + (self.kc_multiplier * df['atr'])
            df['kc_lower'] = df['kc_middle'] - (self.kc_multiplier * df['atr'])
            
            # TTM Squeeze condition (Bollinger Bands inside Keltner Channels)
            df['squeeze_on'] = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])
            
            # TTM Histogram (momentum oscillator)
            df['ttm_histogram'] = self._calculate_momentum_histogram(df)
            
            # EMAs for trend analysis
            df['ema5'] = df['close'].ewm(span=5).mean()
            df['ema8'] = df['close'].ewm(span=8).mean()
            df['ema21'] = df['close'].ewm(span=21).mean()
            
            # Volume analysis
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # Momentum indicators
            df['momentum'] = df['close'] - df['close'].shift(4)
            df['roc'] = ((df['close'] - df['close'].shift(10)) / df['close'].shift(10)) * 100
            
            # Clean up temporary columns
            temp_cols = ['prev_close', 'tr1', 'tr2', 'tr3', 'true_range', 'bb_std']
            df.drop(columns=[col for col in temp_cols if col in df.columns], inplace=True)
            
            return df.dropna()
            
        except Exception as e:
            self.logger.error(f"Error calculating TTM indicators: {e}")
            return df
    
    def _calculate_momentum_histogram(self, df: pd.DataFrame) -> pd.Series:
        """Calculate TTM momentum histogram using linear regression"""
        def linear_regression_slope(series):
            if len(series) < 2:
                return 0
            x = np.arange(len(series))
            y = series.values
            try:
                slope = np.polyfit(x, y, 1)[0]
                return slope * 100  # Scale for better visualization
            except:
                return 0
        
        return df['close'].rolling(window=self.momentum_period).apply(linear_regression_slope, raw=False)
    
    def detect_3_bar_confirmation(self, df: pd.DataFrame) -> Optional[TTMSqueezeSignal]:
        """Detect TTM Squeeze signals with 3-bar confirmation"""
        if df.empty or len(df) < 10:
            return None
        
        try:
            # Get recent data for analysis
            recent = df.tail(10)
            current = recent.iloc[-1]
            
            # Check for squeeze conditions
            squeeze_conditions = self._check_squeeze_conditions(recent)
            if not squeeze_conditions['valid']:
                return None
            
            # 3-bar histogram confirmation
            histogram_bars = recent['ttm_histogram'].tail(self.confirmation_bars).tolist()
            three_bar_confirmation = self._validate_3_bar_pattern(histogram_bars)
            
            if not three_bar_confirmation['valid']:
                return None
            
            # Additional confirmations
            momentum_confirmation = self._check_momentum_confirmation(recent)
            volume_confirmation = current['volume_ratio'] > self.min_volume_ratio
            ema_alignment = self._check_ema_alignment(current)
            
            # Multi-timeframe trend analysis
            weekly_trend, daily_trend = self._analyze_trends(df)
            
            # Calculate signal parameters
            entry_price = float(current['close'])
            signal_type = three_bar_confirmation['direction']
            
            if signal_type == 'bullish_breakout':
                target_price = entry_price * 1.04  # 4% target
                stop_loss = entry_price * 0.98     # 2% stop
            else:
                target_price = entry_price * 0.96  # 4% target (short)
                stop_loss = entry_price * 1.02     # 2% stop (short)
            
            # Calculate confidence score
            confidence = self._calculate_confidence_score(
                three_bar_confirmation, momentum_confirmation, 
                volume_confirmation, ema_alignment, weekly_trend, daily_trend
            )
            
            # Risk/reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(target_price - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            # Position sizing (conservative)
            position_size_percent = min(10.0, confidence * 15)  # Max 10% position
            
            return TTMSqueezeSignal(
                symbol=current.name if hasattr(current, 'name') else "UNKNOWN",
                signal_type=signal_type,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=confidence,
                timeframe="1day",
                timestamp=datetime.now(),
                histogram_bars=histogram_bars,
                momentum_confirmation=momentum_confirmation,
                volume_confirmation=volume_confirmation,
                ema_alignment=ema_alignment,
                weekly_trend=weekly_trend,
                daily_trend=daily_trend,
                risk_reward_ratio=risk_reward_ratio,
                position_size_percent=position_size_percent
            )
            
        except Exception as e:
            self.logger.error(f"Error in 3-bar confirmation: {e}")
            return None
    
    def _check_squeeze_conditions(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Check core TTM Squeeze conditions"""
        try:
            current = df.iloc[-1]
            recent_squeeze = df['squeeze_on'].tail(5).any()  # Squeeze in last 5 bars
            current_squeeze = bool(current['squeeze_on'])
            
            # Squeeze release (was squeezed recently, now breaking out)
            squeeze_release = recent_squeeze and not current_squeeze
            
            return {
                'valid': squeeze_release or current_squeeze,
                'current_squeeze': current_squeeze,
                'recent_squeeze': recent_squeeze,
                'squeeze_release': squeeze_release
            }
        except Exception as e:
            self.logger.error(f"Error checking squeeze conditions: {e}")
            return {'valid': False}
    
    def _validate_3_bar_pattern(self, histogram_bars: List[float]) -> Dict[str, Any]:
        """Validate 3-bar histogram pattern for breakout confirmation"""
        if len(histogram_bars) < 3:
            return {'valid': False}
        
        try:
            # Get last 3 bars
            bar1, bar2, bar3 = histogram_bars[-3:]
            
            # Bullish pattern: 3 declining bars followed by uptick
            bullish_decline = bar1 > bar2 > bar3  # 3 declining bars
            bullish_uptick = bar3 > bar2 if len(histogram_bars) > 3 else False
            
            # Bearish pattern: 3 rising bars followed by downtick  
            bearish_rise = bar1 < bar2 < bar3  # 3 rising bars
            bearish_downtick = bar3 < bar2 if len(histogram_bars) > 3 else False
            
            if bullish_decline and abs(bar3) > 0.1:  # Minimum momentum threshold
                return {
                    'valid': True,
                    'direction': 'bullish_breakout',
                    'pattern_strength': abs(bar1 - bar3)
                }
            elif bearish_rise and abs(bar3) > 0.1:
                return {
                    'valid': True,
                    'direction': 'bearish_breakout', 
                    'pattern_strength': abs(bar3 - bar1)
                }
            else:
                return {'valid': False}
                
        except Exception as e:
            self.logger.error(f"Error validating 3-bar pattern: {e}")
            return {'valid': False}
    
    def _check_momentum_confirmation(self, df: pd.DataFrame) -> bool:
        """Check momentum confirmation"""
        try:
            current = df.iloc[-1]
            return (
                current['momentum'] > 0 and 
                current['roc'] > -5  # Not in severe downtrend
            )
        except:
            return False
    
    def _check_ema_alignment(self, current: pd.Series) -> bool:
        """Check EMA alignment for trend confirmation"""
        try:
            return (
                current['close'] > current['ema5'] and
                current['ema5'] > current['ema8'] and
                current['ema8'] > current['ema21']
            )
        except:
            return False
    
    def _analyze_trends(self, df: pd.DataFrame) -> Tuple[str, str]:
        """Analyze weekly and daily trends"""
        try:
            # Simple trend analysis using EMAs
            current = df.iloc[-1]
            
            # Daily trend
            if current['ema8'] > current['ema21']:
                daily_trend = 'bullish'
            elif current['ema8'] < current['ema21']:
                daily_trend = 'bearish'
            else:
                daily_trend = 'neutral'
            
            # Weekly trend (using longer period)
            if len(df) >= 50:
                ema50 = df['close'].ewm(span=50).mean().iloc[-1]
                if current['close'] > ema50:
                    weekly_trend = 'bullish'
                elif current['close'] < ema50:
                    weekly_trend = 'bearish'
                else:
                    weekly_trend = 'neutral'
            else:
                weekly_trend = daily_trend
            
            return weekly_trend, daily_trend
            
        except Exception as e:
            self.logger.error(f"Error analyzing trends: {e}")
            return 'neutral', 'neutral'
    
    def _calculate_confidence_score(self, three_bar: Dict, momentum: bool, 
                                  volume: bool, ema: bool, weekly: str, daily: str) -> float:
        """Calculate overall confidence score"""
        try:
            score = 0.0
            
            # Base score from 3-bar pattern
            if three_bar.get('valid'):
                score += 0.4
                pattern_strength = three_bar.get('pattern_strength', 0)
                score += min(0.2, pattern_strength * 0.1)  # Bonus for strong pattern
            
            # Confirmation bonuses
            if momentum:
                score += 0.15
            if volume:
                score += 0.15
            if ema:
                score += 0.15
            
            # Trend alignment bonus
            if weekly == daily and weekly != 'neutral':
                score += 0.1
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {e}")
            return 0.5

async def test_enhanced_ttm_scanner():
    """Test the enhanced TTM Squeeze scanner"""
    print("[SEARCH] Testing Enhanced TTM Squeeze Scanner")
    print("=" * 50)
    
    scanner = EnhancedTTMSqueezeScanner()
    
    # Test symbols
    test_symbols = ["AAPL", "TSLA", "MSFT", "GOOGL", "NVDA"]
    
    for symbol in test_symbols:
        print(f"\n[DATA] Analyzing {symbol}...")
        
        # Fetch historical data
        df = await scanner.fetch_historical_data(symbol, "1day", 100)
        
        if df.empty:
            print(f"   [ERROR] No data available for {symbol}")
            continue
        
        # Calculate indicators
        df = scanner.calculate_ttm_indicators(df)
        
        if df.empty:
            print(f"   [ERROR] Insufficient data for indicators")
            continue
        
        # Detect signals
        signal = scanner.detect_3_bar_confirmation(df)
        
        if signal:
            print(f"   [TARGET] TTM Squeeze Signal Detected!")
            print(f"   [UP] Type: {signal.signal_type}")
            print(f"   [MONEY] Entry: ${signal.entry_price:.2f}")
            print(f"   [TARGET] Target: ${signal.target_price:.2f}")
            print(f"   [SHIELD] Stop: ${signal.stop_loss:.2f}")
            print(f"   [STAR] Confidence: {signal.confidence:.1%}")
            print(f"   [DATA] R/R Ratio: {signal.risk_reward_ratio:.2f}")
            print(f"   [UP] Trends: Weekly={signal.weekly_trend}, Daily={signal.daily_trend}")
            print(f"   [OK] Confirmations: Momentum={signal.momentum_confirmation}, Volume={signal.volume_confirmation}, EMA={signal.ema_alignment}")
        else:
            print(f"   ⏳ No TTM Squeeze signals detected")
    
    print(f"\n[OK] Enhanced TTM Squeeze Scanner test complete!")

if __name__ == "__main__":
    asyncio.run(test_enhanced_ttm_scanner())
