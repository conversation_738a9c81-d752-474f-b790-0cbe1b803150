#!/usr/bin/env python3
"""
A.T.L.A.S. Robust Server Manager
Enhanced error handling and server management for reorganized project structure
"""

import sys
import os
import logging
import asyncio
import signal
import time
import threading
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any
import uvicorn
from contextlib import asynccontextmanager

# Add project paths for reorganized structure
project_root = Path(__file__).parent.parent
sys.path.extend([
    str(project_root),
    str(project_root / "1_main_chat_engine"),
    str(project_root / "2_trading_logic"),
    str(project_root / "3_market_news_data"),
    str(project_root / "4_helper_tools")
])

# Configure ASCII-safe logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(project_root / 'atlas_server.log', encoding='utf-8', errors='replace')
    ]
)

logger = logging.getLogger(__name__)

class AtlasRobustServerManager:
    """Robust server manager with enhanced error handling and recovery"""
    
    def __init__(self):
        self.project_root = project_root
        self.server_process = None
        self.is_running = False
        self.restart_count = 0
        self.max_restarts = 5
        self.health_check_interval = 30
        self.server_config = {
            'host': '0.0.0.0',
            'port': 8080,
            'log_level': 'info',
            'access_log': True,
            'reload': False  # Disable in production
        }
        
    def validate_server_environment(self):
        """Validate server environment and dependencies"""
        logger.info("Validating server environment...")
        
        # Check critical files
        critical_files = [
            self.project_root / "1_main_chat_engine" / "atlas_server.py",
            self.project_root / "4_helper_tools" / "config.py",
            self.project_root / "databases"
        ]
        
        for file_path in critical_files:
            if not file_path.exists():
                logger.error(f"Critical file/directory missing: {file_path}")
                return False
            logger.info(f"Found: {file_path}")
        
        # Check Python dependencies
        try:
            import fastapi
            import uvicorn
            import pandas
            import numpy
            logger.info("Core dependencies validated")
        except ImportError as e:
            logger.error(f"Missing dependency: {e}")
            return False
        
        return True
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.shutdown_server()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        if hasattr(signal, 'SIGBREAK'):  # Windows
            signal.signal(signal.SIGBREAK, signal_handler)
    
    def load_server_app(self):
        """Load the FastAPI application with error handling"""
        try:
            # Import the main server application
            sys.path.insert(0, str(self.project_root / "1_main_chat_engine"))
            from atlas_server import app
            
            logger.info("A.T.L.A.S. FastAPI application loaded successfully")
            return app
            
        except ImportError as e:
            logger.error(f"Failed to import A.T.L.A.S. server application: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error loading server app: {e}")
            return None
    
    def check_port_availability(self, port: int) -> bool:
        """Check if the specified port is available"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0  # Port is available if connection fails
        except Exception:
            return False
    
    def find_available_port(self, start_port: int = 8080) -> int:
        """Find an available port starting from the specified port"""
        for port in range(start_port, start_port + 100):
            if self.check_port_availability(port):
                return port
        
        logger.error("No available ports found in range")
        return start_port  # Fallback to original port
    
    async def health_check(self):
        """Perform health check on the running server"""
        try:
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"http://localhost:{self.server_config['port']}/health") as response:
                    if response.status == 200:
                        return True
                    else:
                        logger.warning(f"Health check failed with status: {response.status}")
                        return False
                        
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False
    
    async def monitor_server_health(self):
        """Monitor server health and restart if necessary"""
        while self.is_running:
            await asyncio.sleep(self.health_check_interval)
            
            if self.is_running:
                healthy = await self.health_check()
                if not healthy:
                    logger.warning("Server health check failed, attempting restart...")
                    await self.restart_server()
    
    async def restart_server(self):
        """Restart the server with exponential backoff"""
        if self.restart_count >= self.max_restarts:
            logger.error(f"Maximum restart attempts ({self.max_restarts}) reached")
            self.is_running = False
            return False
        
        self.restart_count += 1
        backoff_time = min(2 ** self.restart_count, 60)  # Max 60 seconds
        
        logger.info(f"Restarting server (attempt {self.restart_count}/{self.max_restarts})")
        logger.info(f"Waiting {backoff_time} seconds before restart...")
        
        await asyncio.sleep(backoff_time)
        
        # Stop current server
        self.shutdown_server()
        
        # Wait a moment
        await asyncio.sleep(2)
        
        # Start new server
        return await self.start_server()
    
    async def start_server(self):
        """Start the A.T.L.A.S. server with robust error handling"""
        logger.info("Starting A.T.L.A.S. robust server...")
        
        # Validate environment
        if not self.validate_server_environment():
            logger.error("Server environment validation failed")
            return False
        
        # Load application
        app = self.load_server_app()
        if app is None:
            logger.error("Failed to load server application")
            return False
        
        # Check port availability
        if not self.check_port_availability(self.server_config['port']):
            logger.warning(f"Port {self.server_config['port']} is busy")
            new_port = self.find_available_port(self.server_config['port'] + 1)
            logger.info(f"Using alternative port: {new_port}")
            self.server_config['port'] = new_port
        
        try:
            # Setup signal handlers
            self.setup_signal_handlers()
            
            # Configure uvicorn server
            config = uvicorn.Config(
                app,
                host=self.server_config['host'],
                port=self.server_config['port'],
                log_level=self.server_config['log_level'],
                access_log=self.server_config['access_log'],
                reload=self.server_config['reload']
            )
            
            server = uvicorn.Server(config)
            self.server_process = server
            self.is_running = True
            
            logger.info(f"A.T.L.A.S. server starting on http://{self.server_config['host']}:{self.server_config['port']}")
            
            # Start health monitoring in background
            health_monitor_task = asyncio.create_task(self.monitor_server_health())
            
            # Start the server
            await server.serve()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start server: {e}")
            self.is_running = False
            return False
    
    def shutdown_server(self):
        """Gracefully shutdown the server"""
        logger.info("Shutting down A.T.L.A.S. server...")
        
        self.is_running = False
        
        if self.server_process:
            try:
                if hasattr(self.server_process, 'should_exit'):
                    self.server_process.should_exit = True
                logger.info("Server shutdown initiated")
            except Exception as e:
                logger.error(f"Error during server shutdown: {e}")
    
    def run_server(self):
        """Run the server with error handling"""
        try:
            asyncio.run(self.start_server())
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            self.shutdown_server()

def main():
    """Main function to start the robust server manager"""
    logger.info("=" * 60)
    logger.info("A.T.L.A.S. ROBUST SERVER MANAGER")
    logger.info("Advanced Trading & Learning Analysis System")
    logger.info("=" * 60)
    
    try:
        server_manager = AtlasRobustServerManager()
        server_manager.run_server()
        
    except Exception as e:
        logger.error(f"Critical error in server manager: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
