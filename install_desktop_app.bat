@echo off
echo ======================================================================
echo 🚀 A.T.L.A.S. LEE METHOD SCANNER - DESKTOP APP INSTALLER
echo ======================================================================
echo Advanced Trading ^& Learning Analysis System
echo Converting to Professional Desktop Application
echo ======================================================================

echo [STEP 1] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js from https://nodejs.org/
    echo    Download the LTS version and run this script again.
    pause
    exit /b 1
)
echo ✅ Node.js found

echo.
echo [STEP 2] Installing Electron dependencies...
call npm install electron --save-dev
if %errorlevel% neq 0 (
    echo ❌ Failed to install Electron
    pause
    exit /b 1
)
echo ✅ Electron installed

echo.
echo [STEP 3] Installing Electron Builder (for creating installers)...
call npm install electron-builder --save-dev
if %errorlevel% neq 0 (
    echo ❌ Failed to install Electron Builder
    pause
    exit /b 1
)
echo ✅ Electron Builder installed

echo.
echo [STEP 4] Creating application icon...
echo Creating simple icon placeholder...
echo. > icon.png
echo ✅ Icon placeholder created

echo.
echo ======================================================================
echo 🎉 INSTALLATION COMPLETE!
echo ======================================================================
echo.
echo To start your A.T.L.A.S. Desktop App:
echo   npm start
echo.
echo To build distributable installer:
echo   npm run dist
echo.
echo Features of your new desktop app:
echo • ✅ No CORS restrictions - full API access
echo • ✅ Native desktop experience  
echo • ✅ Keyboard shortcuts
echo • ✅ Professional menus
echo • ✅ Cross-platform compatibility
echo • ✅ Can be distributed as installer
echo.
echo ======================================================================

pause
