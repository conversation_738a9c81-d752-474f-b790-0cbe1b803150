#!/usr/bin/env python3
"""
A.T.L.A.S. Minimal Startup Script
Unicode-safe initialization with enhanced error handling for reorganized project structure
"""

import sys
import os
import logging
import asyncio
import subprocess
import time
from pathlib import Path

# Add project directories to Python path for reorganized structure
project_root = Path(__file__).parent
sys.path.extend([
    str(project_root),
    str(project_root / "1_main_chat_engine"),
    str(project_root / "2_trading_logic"),
    str(project_root / "3_market_news_data"),
    str(project_root / "4_helper_tools")
])

# Configure Unicode-safe logging
def setup_unicode_safe_logging():
    """Setup logging with ASCII-safe formatting to prevent Unicode errors"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('atlas_startup.log', encoding='utf-8', errors='replace')
        ]
    )
    
    # Ensure all log messages are ASCII-safe
    class ASCIISafeFormatter(logging.Formatter):
        def format(self, record):
            # Convert any Unicode characters to ASCII-safe equivalents
            msg = super().format(record)
            return msg.encode('ascii', errors='replace').decode('ascii')
    
    formatter = ASCIISafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    for handler in logging.getLogger().handlers:
        handler.setFormatter(formatter)

logger = logging.getLogger(__name__)

class AtlasMinimalStartup:
    """Minimal startup manager for A.T.L.A.S. system with reorganized structure"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.components = {
            'databases': self.project_root / 'databases',
            'main_engine': self.project_root / '1_main_chat_engine',
            'trading_logic': self.project_root / '2_trading_logic',
            'market_data': self.project_root / '3_market_news_data',
            'helper_tools': self.project_root / '4_helper_tools',
            'desktop_app': self.project_root / 'desktop_app',
            'tests': self.project_root / 'tests'
        }
        self.startup_mode = 'minimal'
        
    def validate_project_structure(self):
        """Validate reorganized project structure"""
        logger.info("Validating A.T.L.A.S. project structure...")
        
        missing_components = []
        for name, path in self.components.items():
            if not path.exists():
                missing_components.append(f"{name}: {path}")
                logger.warning(f"Missing component: {name} at {path}")
            else:
                logger.info(f"Found component: {name}")
        
        if missing_components:
            logger.error("Missing project components:")
            for component in missing_components:
                logger.error(f"  - {component}")
            return False
        
        logger.info("Project structure validation: PASSED")
        return True
    
    def check_dependencies(self):
        """Check critical Python dependencies"""
        logger.info("Checking Python dependencies...")
        
        critical_deps = [
            'fastapi', 'uvicorn', 'pandas', 'numpy', 
            'requests', 'sqlite3', 'asyncio'
        ]
        
        missing_deps = []
        for dep in critical_deps:
            try:
                __import__(dep)
                logger.info(f"Dependency check: {dep} - OK")
            except ImportError:
                missing_deps.append(dep)
                logger.warning(f"Dependency check: {dep} - MISSING")
        
        if missing_deps:
            logger.error("Missing dependencies:")
            for dep in missing_deps:
                logger.error(f"  - {dep}")
            logger.info("Install missing dependencies with: pip install -r requirements.txt")
            return False
        
        logger.info("Dependency check: PASSED")
        return True
    
    def initialize_databases(self):
        """Initialize database connections with reorganized structure"""
        logger.info("Initializing database connections...")
        
        db_path = self.components['databases']
        if not db_path.exists():
            logger.error(f"Database directory not found: {db_path}")
            return False
        
        # Check for critical database files
        critical_dbs = ['atlas.db', 'atlas_memory.db', 'atlas_rag.db']
        for db_file in critical_dbs:
            db_full_path = db_path / db_file
            if db_full_path.exists():
                logger.info(f"Database found: {db_file}")
            else:
                logger.warning(f"Database missing: {db_file} (will be created)")
        
        logger.info("Database initialization: COMPLETED")
        return True
    
    def start_core_services(self):
        """Start core A.T.L.A.S. services"""
        logger.info("Starting core A.T.L.A.S. services...")
        
        try:
            # Import core components with error handling
            sys.path.insert(0, str(self.components['main_engine']))
            
            # Try to import and start the main server
            try:
                from atlas_server import app
                logger.info("Main A.T.L.A.S. server imported successfully")
                return True
            except ImportError as e:
                logger.error(f"Failed to import main server: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting core services: {e}")
            return False
    
    def start_lee_method_scanner(self):
        """Start Lee Method scanner with error handling"""
        logger.info("Initializing Lee Method scanner...")
        
        try:
            # Import Lee Method components
            from lee_method_scanner import LeeMethodScanner
            from atlas_lee_method_realtime_scanner import AtlasLeeMethodRealtimeScanner
            
            logger.info("Lee Method scanner components loaded successfully")
            return True
            
        except ImportError as e:
            logger.warning(f"Lee Method scanner not available: {e}")
            logger.info("System will continue without Lee Method scanner")
            return False
        except Exception as e:
            logger.error(f"Error initializing Lee Method scanner: {e}")
            return False
    
    def run_startup_sequence(self):
        """Execute complete startup sequence"""
        logger.info("=" * 60)
        logger.info("A.T.L.A.S. MINIMAL STARTUP SEQUENCE")
        logger.info("Advanced Trading & Learning Analysis System")
        logger.info("=" * 60)
        
        startup_steps = [
            ("Project Structure Validation", self.validate_project_structure),
            ("Dependency Check", self.check_dependencies),
            ("Database Initialization", self.initialize_databases),
            ("Core Services", self.start_core_services),
            ("Lee Method Scanner", self.start_lee_method_scanner)
        ]
        
        failed_steps = []
        for step_name, step_func in startup_steps:
            logger.info(f"Executing: {step_name}")
            try:
                if step_func():
                    logger.info(f"SUCCESS: {step_name}")
                else:
                    logger.warning(f"FAILED: {step_name}")
                    failed_steps.append(step_name)
            except Exception as e:
                logger.error(f"ERROR in {step_name}: {e}")
                failed_steps.append(step_name)
        
        # Startup summary
        logger.info("=" * 60)
        if failed_steps:
            logger.warning("STARTUP COMPLETED WITH WARNINGS")
            logger.warning("Failed steps:")
            for step in failed_steps:
                logger.warning(f"  - {step}")
            logger.info("System may have reduced functionality")
        else:
            logger.info("STARTUP COMPLETED SUCCESSFULLY")
            logger.info("All A.T.L.A.S. components initialized")
        
        logger.info("=" * 60)
        return len(failed_steps) == 0

def main():
    """Main startup function"""
    # Setup Unicode-safe logging first
    setup_unicode_safe_logging()
    
    try:
        startup_manager = AtlasMinimalStartup()
        success = startup_manager.run_startup_sequence()
        
        if success:
            logger.info("A.T.L.A.S. system ready for operation")
            logger.info("You can now start the main server with:")
            logger.info("  python 1_main_chat_engine/atlas_server.py")
            logger.info("Or start the Lee Method scanner with:")
            logger.info("  python start_lee_method_system.py")
            return 0
        else:
            logger.warning("A.T.L.A.S. startup completed with issues")
            logger.info("Check the logs above for details")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Startup interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during startup: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
