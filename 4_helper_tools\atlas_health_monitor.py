#!/usr/bin/env python3
"""
A.T.L.A.S. System Health Monitoring
Real-time status dashboard and performance metrics for all A.T.L.A.S. services
"""

import sys
import os
import asyncio
import time
import logging
import json
import psutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import aiohttp
from dataclasses import dataclass, asdict

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.extend([
    str(project_root),
    str(project_root / "4_helper_tools")
])

logger = logging.getLogger(__name__)

@dataclass
class ServiceHealth:
    """Health status for a service"""
    name: str
    status: str  # healthy, unhealthy, degraded, unknown
    response_time: float
    last_check: datetime
    error_count: int
    uptime: float
    details: Dict[str, Any]

@dataclass
class SystemMetrics:
    """System performance metrics"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    timestamp: datetime

class AtlasHealthMonitor:
    """Comprehensive health monitoring system for A.T.L.A.S."""
    
    def __init__(self):
        self.project_root = project_root
        self.services = {}
        self.metrics_history = []
        self.max_history = 1000
        self.monitoring_interval = 30
        self.alert_thresholds = {
            'cpu_percent': 80,
            'memory_percent': 85,
            'disk_percent': 90,
            'response_time': 5.0,
            'error_rate': 0.1
        }
        self.service_endpoints = {
            'atlas_main': 'http://localhost:8080/health',
            'lee_method_api': 'http://localhost:5001/health',
            'atlas_server': 'http://localhost:8080/api/status',
        }
        
    async def check_service_health(self, service_name: str, endpoint: str) -> ServiceHealth:
        """Check health of a specific service"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get(endpoint) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        status = 'healthy'
                        details = data
                    else:
                        status = 'unhealthy'
                        details = {'status_code': response.status}
                        
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            status = 'unhealthy'
            details = {'error': 'timeout'}
            
        except Exception as e:
            response_time = time.time() - start_time
            status = 'unhealthy'
            details = {'error': str(e)}
        
        # Get or create service health record
        if service_name in self.services:
            prev_health = self.services[service_name]
            error_count = prev_health.error_count + (1 if status != 'healthy' else 0)
            uptime = prev_health.uptime + self.monitoring_interval if status == 'healthy' else 0
        else:
            error_count = 1 if status != 'healthy' else 0
            uptime = self.monitoring_interval if status == 'healthy' else 0
        
        return ServiceHealth(
            name=service_name,
            status=status,
            response_time=response_time,
            last_check=datetime.now(),
            error_count=error_count,
            uptime=uptime,
            details=details
        )
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Network I/O
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # Process count
            process_count = len(psutil.pids())
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                process_count=process_count,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return SystemMetrics(
                cpu_percent=0,
                memory_percent=0,
                disk_percent=0,
                network_io={},
                process_count=0,
                timestamp=datetime.now()
            )
    
    def check_atlas_processes(self) -> Dict[str, Any]:
        """Check for running A.T.L.A.S. processes"""
        atlas_processes = {}
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
                try:
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    
                    # Check for A.T.L.A.S. related processes
                    if any(keyword in cmdline.lower() for keyword in [
                        'atlas_server', 'atlas_comprehensive', 'lee_method', 
                        'atlas_robust', 'atlas_demo'
                    ]):
                        atlas_processes[proc.info['pid']] = {
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_percent': proc.info['memory_percent'],
                            'status': proc.status()
                        }
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            logger.error(f"Error checking A.T.L.A.S. processes: {e}")
        
        return atlas_processes
    
    def check_database_health(self) -> Dict[str, Any]:
        """Check health of A.T.L.A.S. databases"""
        db_health = {}
        db_path = self.project_root / "databases"
        
        if not db_path.exists():
            return {'status': 'error', 'message': 'Database directory not found'}
        
        try:
            import sqlite3
            
            db_files = [
                'atlas.db', 'atlas_memory.db', 'atlas_rag.db',
                'atlas_compliance.db', 'atlas_feedback.db',
                'atlas_enhanced_memory.db', 'atlas_auto_trading.db'
            ]
            
            for db_file in db_files:
                db_full_path = db_path / db_file
                
                if db_full_path.exists():
                    try:
                        # Test database connection
                        conn = sqlite3.connect(str(db_full_path))
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        conn.close()
                        
                        db_health[db_file] = {
                            'status': 'healthy',
                            'size_mb': round(db_full_path.stat().st_size / (1024 * 1024), 2),
                            'tables': len(tables),
                            'last_modified': datetime.fromtimestamp(db_full_path.stat().st_mtime).isoformat()
                        }
                        
                    except Exception as e:
                        db_health[db_file] = {
                            'status': 'error',
                            'error': str(e)
                        }
                else:
                    db_health[db_file] = {
                        'status': 'missing'
                    }
                    
        except ImportError:
            db_health['error'] = 'sqlite3 not available'
        except Exception as e:
            db_health['error'] = str(e)
        
        return db_health
    
    def check_file_system_health(self) -> Dict[str, Any]:
        """Check A.T.L.A.S. file system health"""
        fs_health = {}
        
        # Check critical directories
        critical_dirs = [
            '1_main_chat_engine', '2_trading_logic', '3_market_news_data',
            '4_helper_tools', 'databases', 'desktop_app', 'tests'
        ]
        
        for directory in critical_dirs:
            dir_path = self.project_root / directory
            if dir_path.exists():
                try:
                    file_count = len(list(dir_path.rglob('*.py')))
                    total_size = sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())
                    
                    fs_health[directory] = {
                        'status': 'healthy',
                        'python_files': file_count,
                        'total_size_mb': round(total_size / (1024 * 1024), 2)
                    }
                except Exception as e:
                    fs_health[directory] = {
                        'status': 'error',
                        'error': str(e)
                    }
            else:
                fs_health[directory] = {
                    'status': 'missing'
                }
        
        return fs_health
    
    async def perform_comprehensive_health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check of entire A.T.L.A.S. system"""
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'services': {},
            'system_metrics': {},
            'atlas_processes': {},
            'database_health': {},
            'file_system_health': {},
            'alerts': []
        }
        
        try:
            # Check service health
            for service_name, endpoint in self.service_endpoints.items():
                service_health = await self.check_service_health(service_name, endpoint)
                health_report['services'][service_name] = asdict(service_health)
                self.services[service_name] = service_health
            
            # Get system metrics
            metrics = self.get_system_metrics()
            health_report['system_metrics'] = asdict(metrics)
            
            # Add to metrics history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history:
                self.metrics_history.pop(0)
            
            # Check A.T.L.A.S. processes
            health_report['atlas_processes'] = self.check_atlas_processes()
            
            # Check database health
            health_report['database_health'] = self.check_database_health()
            
            # Check file system health
            health_report['file_system_health'] = self.check_file_system_health()
            
            # Generate alerts
            alerts = self.generate_alerts(health_report)
            health_report['alerts'] = alerts
            
            # Determine overall status
            health_report['overall_status'] = self.determine_overall_status(health_report)
            
        except Exception as e:
            logger.error(f"Error during comprehensive health check: {e}")
            health_report['error'] = str(e)
            health_report['overall_status'] = 'error'
        
        return health_report
    
    def generate_alerts(self, health_report: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate alerts based on health report"""
        alerts = []
        
        # Check system metrics alerts
        metrics = health_report.get('system_metrics', {})
        
        if metrics.get('cpu_percent', 0) > self.alert_thresholds['cpu_percent']:
            alerts.append({
                'type': 'warning',
                'component': 'system',
                'message': f"High CPU usage: {metrics['cpu_percent']:.1f}%",
                'threshold': self.alert_thresholds['cpu_percent']
            })
        
        if metrics.get('memory_percent', 0) > self.alert_thresholds['memory_percent']:
            alerts.append({
                'type': 'warning',
                'component': 'system',
                'message': f"High memory usage: {metrics['memory_percent']:.1f}%",
                'threshold': self.alert_thresholds['memory_percent']
            })
        
        # Check service alerts
        for service_name, service_data in health_report.get('services', {}).items():
            if service_data.get('status') != 'healthy':
                alerts.append({
                    'type': 'error',
                    'component': 'service',
                    'message': f"Service {service_name} is {service_data.get('status')}",
                    'service': service_name
                })
            
            if service_data.get('response_time', 0) > self.alert_thresholds['response_time']:
                alerts.append({
                    'type': 'warning',
                    'component': 'performance',
                    'message': f"Slow response from {service_name}: {service_data['response_time']:.2f}s",
                    'service': service_name
                })
        
        return alerts
    
    def determine_overall_status(self, health_report: Dict[str, Any]) -> str:
        """Determine overall system status"""
        alerts = health_report.get('alerts', [])
        services = health_report.get('services', {})
        
        # Check for critical errors
        error_alerts = [a for a in alerts if a.get('type') == 'error']
        if error_alerts:
            return 'unhealthy'
        
        # Check service health
        unhealthy_services = [s for s in services.values() if s.get('status') != 'healthy']
        if len(unhealthy_services) > len(services) / 2:
            return 'degraded'
        
        # Check for warnings
        warning_alerts = [a for a in alerts if a.get('type') == 'warning']
        if warning_alerts:
            return 'degraded'
        
        return 'healthy'
    
    def get_health_dashboard_data(self) -> Dict[str, Any]:
        """Get formatted data for health dashboard"""
        if not self.services:
            return {'status': 'no_data', 'message': 'No health data available'}
        
        # Calculate uptime percentages
        service_uptimes = {}
        for service_name, service_health in self.services.items():
            total_time = time.time() - (service_health.last_check.timestamp() - service_health.uptime)
            uptime_percent = (service_health.uptime / total_time) * 100 if total_time > 0 else 0
            service_uptimes[service_name] = min(100, max(0, uptime_percent))
        
        # Get recent metrics
        recent_metrics = self.metrics_history[-10:] if self.metrics_history else []
        
        return {
            'services': {name: asdict(health) for name, health in self.services.items()},
            'service_uptimes': service_uptimes,
            'recent_metrics': [asdict(m) for m in recent_metrics],
            'alert_summary': {
                'total_alerts': sum(len(s.details.get('alerts', [])) for s in self.services.values()),
                'critical_alerts': sum(1 for s in self.services.values() if s.status == 'unhealthy')
            }
        }

# Global health monitor instance
health_monitor = AtlasHealthMonitor()

def get_health_monitor() -> AtlasHealthMonitor:
    """Get global health monitor instance"""
    return health_monitor

async def run_health_monitoring():
    """Run continuous health monitoring"""
    monitor = get_health_monitor()
    
    print("🏥 A.T.L.A.S. Health Monitoring Started")
    print("=" * 50)
    
    try:
        while True:
            health_report = await monitor.perform_comprehensive_health_check()
            
            # Print summary
            status = health_report['overall_status']
            emoji = "✅" if status == "healthy" else "⚠️" if status == "degraded" else "❌"
            
            print(f"\n{emoji} System Status: {status.upper()}")
            print(f"Timestamp: {health_report['timestamp']}")
            
            # Print service status
            for service_name, service_data in health_report.get('services', {}).items():
                service_status = service_data.get('status', 'unknown')
                service_emoji = "✅" if service_status == "healthy" else "❌"
                response_time = service_data.get('response_time', 0)
                print(f"  {service_emoji} {service_name}: {service_status} ({response_time:.3f}s)")
            
            # Print alerts
            alerts = health_report.get('alerts', [])
            if alerts:
                print(f"\n🚨 Alerts ({len(alerts)}):")
                for alert in alerts[:5]:  # Show first 5 alerts
                    print(f"  - {alert['type'].upper()}: {alert['message']}")
            
            # Wait for next check
            await asyncio.sleep(monitor.monitoring_interval)
            
    except KeyboardInterrupt:
        print("\n🛑 Health monitoring stopped")
    except Exception as e:
        print(f"\n❌ Health monitoring error: {e}")

def create_health_dashboard_html() -> str:
    """Create HTML dashboard for health monitoring"""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Health Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', sans-serif; background: #0f1419; color: #ffffff; }
        .header { background: linear-gradient(90deg, #1a1f2e 0%, #2d3748 100%); padding: 20px; text-align: center; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; padding: 20px; }
        .card { background: #1a202c; border: 1px solid #2d3748; border-radius: 8px; padding: 20px; }
        .card h3 { color: #4299e1; margin-bottom: 15px; }
        .status-healthy { color: #48bb78; }
        .status-degraded { color: #ed8936; }
        .status-unhealthy { color: #f56565; }
        .metric { display: flex; justify-content: space-between; margin: 8px 0; }
        .progress-bar { width: 100%; height: 8px; background: #2d3748; border-radius: 4px; overflow: hidden; }
        .progress-fill { height: 100%; transition: width 0.3s ease; }
        .progress-healthy { background: #48bb78; }
        .progress-warning { background: #ed8936; }
        .progress-critical { background: #f56565; }
        .alert { background: #2d1b1b; border-left: 4px solid #f56565; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .refresh-btn { background: #4299e1; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏥 A.T.L.A.S. Health Dashboard</h1>
        <p>Real-time System Monitoring</p>
        <button class="refresh-btn" onclick="refreshData()">Refresh</button>
    </div>

    <div class="dashboard">
        <div class="card">
            <h3>System Overview</h3>
            <div id="system-status">Loading...</div>
        </div>

        <div class="card">
            <h3>Services Status</h3>
            <div id="services-status">Loading...</div>
        </div>

        <div class="card">
            <h3>System Metrics</h3>
            <div id="system-metrics">Loading...</div>
        </div>

        <div class="card">
            <h3>Active Alerts</h3>
            <div id="alerts">Loading...</div>
        </div>
    </div>

    <script>
        async function fetchHealthData() {
            try {
                const response = await fetch('/health/dashboard');
                return await response.json();
            } catch (error) {
                console.error('Error fetching health data:', error);
                return null;
            }
        }

        function updateSystemStatus(data) {
            const container = document.getElementById('system-status');
            if (!data) {
                container.innerHTML = '<div class="status-unhealthy">Unable to fetch data</div>';
                return;
            }

            const status = data.overall_status || 'unknown';
            const statusClass = `status-${status}`;
            container.innerHTML = `
                <div class="${statusClass}">Status: ${status.toUpperCase()}</div>
                <div>Last Updated: ${new Date().toLocaleTimeString()}</div>
            `;
        }

        function updateServicesStatus(services) {
            const container = document.getElementById('services-status');
            if (!services) {
                container.innerHTML = '<div>No service data available</div>';
                return;
            }

            let html = '';
            for (const [name, service] of Object.entries(services)) {
                const statusClass = `status-${service.status}`;
                html += `
                    <div class="metric">
                        <span>${name}</span>
                        <span class="${statusClass}">${service.status}</span>
                    </div>
                `;
            }
            container.innerHTML = html;
        }

        function updateSystemMetrics(metrics) {
            const container = document.getElementById('system-metrics');
            if (!metrics) {
                container.innerHTML = '<div>No metrics available</div>';
                return;
            }

            const cpuClass = metrics.cpu_percent > 80 ? 'progress-critical' : metrics.cpu_percent > 60 ? 'progress-warning' : 'progress-healthy';
            const memClass = metrics.memory_percent > 85 ? 'progress-critical' : metrics.memory_percent > 70 ? 'progress-warning' : 'progress-healthy';

            container.innerHTML = `
                <div class="metric">
                    <span>CPU Usage</span>
                    <span>${metrics.cpu_percent.toFixed(1)}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${cpuClass}" style="width: ${metrics.cpu_percent}%"></div>
                </div>

                <div class="metric">
                    <span>Memory Usage</span>
                    <span>${metrics.memory_percent.toFixed(1)}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill ${memClass}" style="width: ${metrics.memory_percent}%"></div>
                </div>

                <div class="metric">
                    <span>Processes</span>
                    <span>${metrics.process_count}</span>
                </div>
            `;
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('alerts');
            if (!alerts || alerts.length === 0) {
                container.innerHTML = '<div class="status-healthy">No active alerts</div>';
                return;
            }

            let html = '';
            alerts.slice(0, 5).forEach(alert => {
                html += `<div class="alert">${alert.type.toUpperCase()}: ${alert.message}</div>`;
            });
            container.innerHTML = html;
        }

        async function refreshData() {
            const data = await fetchHealthData();
            if (data) {
                updateSystemStatus(data);
                updateServicesStatus(data.services);
                updateSystemMetrics(data.system_metrics);
                updateAlerts(data.alerts);
            }
        }

        // Initial load and auto-refresh
        refreshData();
        setInterval(refreshData, 30000); // Refresh every 30 seconds
    </script>
</body>
</html>
    '''

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='A.T.L.A.S. Health Monitor')
    parser.add_argument('--dashboard', action='store_true', help='Start web dashboard')
    parser.add_argument('--monitor', action='store_true', help='Start continuous monitoring')
    parser.add_argument('--port', type=int, default=9090, help='Dashboard port')

    args = parser.parse_args()

    if args.dashboard:
        from aiohttp import web

        async def dashboard_handler(request):
            return web.Response(text=create_health_dashboard_html(), content_type='text/html')

        async def health_api_handler(request):
            monitor = get_health_monitor()
            health_data = await monitor.perform_comprehensive_health_check()
            return web.json_response(health_data)

        app = web.Application()
        app.router.add_get('/', dashboard_handler)
        app.router.add_get('/health/dashboard', health_api_handler)

        print(f"🌐 A.T.L.A.S. Health Dashboard starting on http://localhost:{args.port}")
        web.run_app(app, host='localhost', port=args.port)

    elif args.monitor:
        asyncio.run(run_health_monitoring())

    else:
        # Default: run single health check
        async def single_check():
            monitor = get_health_monitor()
            health_report = await monitor.perform_comprehensive_health_check()
            print(json.dumps(health_report, indent=2, default=str))

        asyncio.run(single_check())
