#!/usr/bin/env python3
"""
A.T.L.A.S. Error Recovery and Resilience System
Comprehensive error handling, fallback mechanisms, and connection retry logic
"""

import sys
import os
import logging
import asyncio
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, Callable, Union
from functools import wraps
import json

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.extend([
    str(project_root),
    str(project_root / "4_helper_tools")
])

logger = logging.getLogger(__name__)

class AtlasErrorRecovery:
    """Comprehensive error recovery and resilience system"""
    
    def __init__(self):
        self.retry_config = {
            'max_retries': 3,
            'base_delay': 1.0,
            'max_delay': 60.0,
            'exponential_base': 2.0
        }
        self.fallback_modes = {
            'api_failure': 'demo_mode',
            'database_failure': 'memory_mode',
            'network_failure': 'offline_mode',
            'scanner_failure': 'basic_mode'
        }
        self.error_counts = {}
        self.circuit_breakers = {}
        
    def exponential_backoff(self, attempt: int) -> float:
        """Calculate exponential backoff delay"""
        delay = self.retry_config['base_delay'] * (
            self.retry_config['exponential_base'] ** attempt
        )
        return min(delay, self.retry_config['max_delay'])
    
    def circuit_breaker(self, service_name: str, failure_threshold: int = 5, reset_timeout: int = 300):
        """Circuit breaker decorator for service calls"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                now = time.time()
                
                # Initialize circuit breaker state
                if service_name not in self.circuit_breakers:
                    self.circuit_breakers[service_name] = {
                        'failures': 0,
                        'last_failure': 0,
                        'state': 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
                    }
                
                breaker = self.circuit_breakers[service_name]
                
                # Check if circuit is open and should reset
                if breaker['state'] == 'OPEN':
                    if now - breaker['last_failure'] > reset_timeout:
                        breaker['state'] = 'HALF_OPEN'
                        logger.info(f"Circuit breaker for {service_name} moving to HALF_OPEN")
                    else:
                        raise Exception(f"Circuit breaker OPEN for {service_name}")
                
                try:
                    result = await func(*args, **kwargs)
                    
                    # Success - reset circuit breaker
                    if breaker['state'] in ['HALF_OPEN', 'CLOSED']:
                        breaker['failures'] = 0
                        breaker['state'] = 'CLOSED'
                    
                    return result
                    
                except Exception as e:
                    breaker['failures'] += 1
                    breaker['last_failure'] = now
                    
                    if breaker['failures'] >= failure_threshold:
                        breaker['state'] = 'OPEN'
                        logger.error(f"Circuit breaker OPEN for {service_name} after {breaker['failures']} failures")
                    
                    raise e
            
            return wrapper
        return decorator
    
    def retry_with_fallback(self, fallback_func: Optional[Callable] = None):
        """Retry decorator with fallback function"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(self.retry_config['max_retries'] + 1):
                    try:
                        if attempt > 0:
                            delay = self.exponential_backoff(attempt - 1)
                            logger.info(f"Retrying {func.__name__} (attempt {attempt}) after {delay:.1f}s delay")
                            await asyncio.sleep(delay)
                        
                        result = await func(*args, **kwargs)
                        
                        # Success - reset error count
                        if func.__name__ in self.error_counts:
                            del self.error_counts[func.__name__]
                        
                        return result
                        
                    except Exception as e:
                        last_exception = e
                        self.error_counts[func.__name__] = self.error_counts.get(func.__name__, 0) + 1
                        
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}")
                        
                        if attempt == self.retry_config['max_retries']:
                            logger.error(f"All retry attempts failed for {func.__name__}")
                            break
                
                # All retries failed - try fallback
                if fallback_func:
                    try:
                        logger.info(f"Attempting fallback for {func.__name__}")
                        return await fallback_func(*args, **kwargs)
                    except Exception as fallback_error:
                        logger.error(f"Fallback also failed for {func.__name__}: {fallback_error}")
                
                # No fallback or fallback failed
                raise last_exception
            
            return wrapper
        return decorator
    
    async def safe_api_call(self, api_func: Callable, *args, **kwargs) -> Dict[str, Any]:
        """Safe API call with error handling and fallback to demo mode"""
        try:
            result = await api_func(*args, **kwargs)
            return {'success': True, 'data': result, 'mode': 'production'}
            
        except Exception as e:
            logger.error(f"API call failed: {e}")
            
            # Try demo mode fallback
            try:
                from atlas_demo_mode import demo_api_call
                
                # Extract endpoint from function name or kwargs
                endpoint = kwargs.get('endpoint', getattr(api_func, '__name__', 'unknown'))
                demo_result = demo_api_call(endpoint, **kwargs)
                
                logger.info(f"Falling back to demo mode for {endpoint}")
                return {'success': True, 'data': demo_result, 'mode': 'demo', 'original_error': str(e)}
                
            except Exception as demo_error:
                logger.error(f"Demo mode fallback also failed: {demo_error}")
                return {
                    'success': False, 
                    'error': str(e), 
                    'fallback_error': str(demo_error),
                    'mode': 'failed'
                }
    
    async def safe_database_operation(self, db_func: Callable, *args, **kwargs) -> Dict[str, Any]:
        """Safe database operation with fallback to in-memory storage"""
        try:
            result = await db_func(*args, **kwargs)
            return {'success': True, 'data': result, 'storage': 'database'}
            
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            
            # Try in-memory fallback
            try:
                # Simple in-memory storage simulation
                memory_storage = getattr(self, '_memory_storage', {})
                
                operation = kwargs.get('operation', 'read')
                key = kwargs.get('key', 'default')
                
                if operation == 'write':
                    memory_storage[key] = kwargs.get('data')
                    self._memory_storage = memory_storage
                    result = {'status': 'written_to_memory'}
                else:
                    result = memory_storage.get(key, {})
                
                logger.info(f"Using in-memory storage fallback for {operation}")
                return {'success': True, 'data': result, 'storage': 'memory', 'original_error': str(e)}
                
            except Exception as memory_error:
                logger.error(f"Memory storage fallback failed: {memory_error}")
                return {
                    'success': False,
                    'error': str(e),
                    'fallback_error': str(memory_error),
                    'storage': 'failed'
                }
    
    def graceful_degradation(self, component_name: str, error: Exception) -> Dict[str, Any]:
        """Implement graceful degradation for failed components"""
        degradation_strategies = {
            'lee_method_scanner': {
                'fallback_mode': 'basic_analysis',
                'reduced_features': ['real_time_scanning'],
                'alternative_data': 'historical_patterns'
            },
            'ai_chat': {
                'fallback_mode': 'template_responses',
                'reduced_features': ['complex_analysis'],
                'alternative_data': 'predefined_responses'
            },
            'market_data': {
                'fallback_mode': 'cached_data',
                'reduced_features': ['real_time_prices'],
                'alternative_data': 'last_known_prices'
            },
            'desktop_app': {
                'fallback_mode': 'web_interface',
                'reduced_features': ['native_notifications'],
                'alternative_data': 'browser_based_ui'
            }
        }
        
        strategy = degradation_strategies.get(component_name, {
            'fallback_mode': 'minimal_functionality',
            'reduced_features': ['advanced_features'],
            'alternative_data': 'basic_data'
        })
        
        logger.warning(f"Graceful degradation for {component_name}: {strategy['fallback_mode']}")
        
        return {
            'component': component_name,
            'status': 'degraded',
            'fallback_mode': strategy['fallback_mode'],
            'disabled_features': strategy['reduced_features'],
            'alternative_source': strategy['alternative_data'],
            'error': str(error),
            'timestamp': time.time()
        }
    
    async def health_check_with_recovery(self, services: Dict[str, Callable]) -> Dict[str, Any]:
        """Perform health checks with automatic recovery attempts"""
        health_status = {}
        
        for service_name, health_func in services.items():
            try:
                # Perform health check
                result = await health_func()
                health_status[service_name] = {
                    'status': 'healthy',
                    'response_time': getattr(result, 'response_time', 0),
                    'last_check': time.time()
                }
                
            except Exception as e:
                logger.error(f"Health check failed for {service_name}: {e}")
                
                # Attempt recovery
                recovery_result = await self.attempt_service_recovery(service_name, e)
                health_status[service_name] = {
                    'status': 'unhealthy',
                    'error': str(e),
                    'recovery_attempted': True,
                    'recovery_result': recovery_result,
                    'last_check': time.time()
                }
        
        return health_status
    
    async def attempt_service_recovery(self, service_name: str, error: Exception) -> Dict[str, Any]:
        """Attempt to recover a failed service"""
        recovery_strategies = {
            'database': self._recover_database_service,
            'api': self._recover_api_service,
            'scanner': self._recover_scanner_service,
            'server': self._recover_server_service
        }
        
        # Determine recovery strategy
        strategy_key = next((key for key in recovery_strategies.keys() if key in service_name.lower()), 'default')
        recovery_func = recovery_strategies.get(strategy_key, self._default_recovery)
        
        try:
            result = await recovery_func(service_name, error)
            logger.info(f"Recovery successful for {service_name}")
            return {'success': True, 'method': strategy_key, 'result': result}
            
        except Exception as recovery_error:
            logger.error(f"Recovery failed for {service_name}: {recovery_error}")
            return {'success': False, 'error': str(recovery_error)}
    
    async def _recover_database_service(self, service_name: str, error: Exception) -> str:
        """Recover database service"""
        # Try to recreate database connection
        from pathlib import Path
        db_path = Path(project_root) / "databases"
        
        if not db_path.exists():
            db_path.mkdir(parents=True, exist_ok=True)
            return "Created missing database directory"
        
        return "Database directory verified"
    
    async def _recover_api_service(self, service_name: str, error: Exception) -> str:
        """Recover API service"""
        # Switch to demo mode
        return "Switched to demo mode"
    
    async def _recover_scanner_service(self, service_name: str, error: Exception) -> str:
        """Recover scanner service"""
        # Restart scanner with basic configuration
        return "Restarted with basic configuration"
    
    async def _recover_server_service(self, service_name: str, error: Exception) -> str:
        """Recover server service"""
        # Try to restart server on different port
        return "Attempted server restart"
    
    async def _default_recovery(self, service_name: str, error: Exception) -> str:
        """Default recovery strategy"""
        return "Applied graceful degradation"
    
    def get_system_resilience_report(self) -> Dict[str, Any]:
        """Generate system resilience report"""
        return {
            'error_counts': self.error_counts,
            'circuit_breakers': {
                name: {
                    'state': breaker['state'],
                    'failures': breaker['failures']
                }
                for name, breaker in self.circuit_breakers.items()
            },
            'active_fallbacks': list(self.fallback_modes.keys()),
            'retry_config': self.retry_config,
            'timestamp': time.time()
        }

# Global error recovery instance
error_recovery = AtlasErrorRecovery()

def get_error_recovery() -> AtlasErrorRecovery:
    """Get global error recovery instance"""
    return error_recovery

# Convenience decorators
def with_retry_and_fallback(fallback_func=None):
    """Convenience decorator for retry with fallback"""
    return error_recovery.retry_with_fallback(fallback_func)

def with_circuit_breaker(service_name: str, failure_threshold: int = 5):
    """Convenience decorator for circuit breaker"""
    return error_recovery.circuit_breaker(service_name, failure_threshold)

async def safe_execute(func: Callable, *args, **kwargs) -> Dict[str, Any]:
    """Safely execute any function with error recovery"""
    try:
        result = await func(*args, **kwargs)
        return {'success': True, 'data': result}
    except Exception as e:
        logger.error(f"Safe execution failed for {func.__name__}: {e}")
        return {'success': False, 'error': str(e), 'traceback': traceback.format_exc()}

if __name__ == "__main__":
    # Test error recovery system
    async def test_error_recovery():
        recovery = AtlasErrorRecovery()
        
        # Test circuit breaker
        @recovery.circuit_breaker('test_service')
        async def failing_service():
            raise Exception("Service unavailable")
        
        # Test retry with fallback
        @recovery.retry_with_fallback(lambda: {'fallback': True})
        async def unreliable_service():
            if time.time() % 2 < 1:
                raise Exception("Random failure")
            return {'success': True}
        
        print("Testing error recovery system...")
        
        # Test graceful degradation
        degradation = recovery.graceful_degradation('lee_method_scanner', Exception("Scanner failed"))
        print(f"Degradation strategy: {degradation}")
        
        # Test resilience report
        report = recovery.get_system_resilience_report()
        print(f"Resilience report: {json.dumps(report, indent=2)}")
    
    asyncio.run(test_error_recovery())
