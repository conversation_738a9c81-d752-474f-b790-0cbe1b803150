#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Startup System
Integrates all improvements: startup scripts, configuration, demo mode, and error recovery
"""

import sys
import os
import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Add project paths for reorganized structure
project_root = Path(__file__).parent
sys.path.extend([
    str(project_root),
    str(project_root / "1_main_chat_engine"),
    str(project_root / "2_trading_logic"),
    str(project_root / "3_market_news_data"),
    str(project_root / "4_helper_tools")
])

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(project_root / 'atlas_comprehensive.log', encoding='utf-8', errors='replace')
    ]
)

logger = logging.getLogger(__name__)

class AtlasComprehensiveStartup:
    """Comprehensive startup system with all improvements integrated"""
    
    def __init__(self):
        self.project_root = project_root
        self.startup_mode = 'comprehensive'
        self.components_status = {}
        self.config_validated = False
        self.demo_mode_enabled = False
        self.error_recovery = None
        
    def print_startup_banner(self):
        """Print comprehensive startup banner"""
        print("=" * 80)
        print("🚀 A.T.L.A.S. COMPREHENSIVE STARTUP SYSTEM")
        print("   Advanced Trading & Learning Analysis System")
        print("   Integrated: Startup Scripts + Configuration + Demo Mode + Error Recovery")
        print("=" * 80)
        print()
    
    async def initialize_error_recovery(self):
        """Initialize error recovery system"""
        try:
            from atlas_error_recovery import get_error_recovery
            self.error_recovery = get_error_recovery()
            logger.info("✅ Error recovery system initialized")
            return True
        except ImportError as e:
            logger.warning(f"Error recovery system not available: {e}")
            return False
    
    async def check_configuration(self):
        """Check and validate configuration"""
        logger.info("🔧 Checking A.T.L.A.S. configuration...")
        
        config_file = self.project_root / "4_helper_tools" / "config.py"
        
        if not config_file.exists():
            logger.warning("Configuration file not found")
            
            # Offer to run setup wizard
            response = input("🧙 Would you like to run the setup wizard? (Y/n): ").strip().lower()
            if response != 'n':
                return await self.run_setup_wizard()
            else:
                logger.info("Continuing without configuration (demo mode will be enabled)")
                self.demo_mode_enabled = True
                return True
        
        # Validate existing configuration
        try:
            sys.path.insert(0, str(self.project_root / "4_helper_tools"))
            import config
            
            # Check for demo mode
            self.demo_mode_enabled = getattr(config, 'DEMO_MODE', False)
            
            # Validate configuration
            if hasattr(config, 'validate_config'):
                errors = config.validate_config()
                if errors:
                    logger.warning("Configuration validation issues:")
                    for error in errors:
                        logger.warning(f"  - {error}")
                    
                    if not self.demo_mode_enabled:
                        logger.info("Enabling demo mode due to configuration issues")
                        self.demo_mode_enabled = True
            
            self.config_validated = True
            logger.info("✅ Configuration validated")
            
            if self.demo_mode_enabled:
                logger.info("🎭 Demo mode enabled")
            
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            logger.info("Enabling demo mode as fallback")
            self.demo_mode_enabled = True
            return True
    
    async def run_setup_wizard(self):
        """Run the interactive setup wizard"""
        try:
            logger.info("🧙 Starting A.T.L.A.S. setup wizard...")
            
            # Import and run setup wizard
            from atlas_setup_wizard import AtlasSetupWizard
            wizard = AtlasSetupWizard()
            success = wizard.run_setup()
            
            if success:
                logger.info("✅ Setup wizard completed successfully")
                self.config_validated = True
                return True
            else:
                logger.warning("Setup wizard failed, enabling demo mode")
                self.demo_mode_enabled = True
                return True
                
        except Exception as e:
            logger.error(f"Setup wizard failed: {e}")
            logger.info("Enabling demo mode as fallback")
            self.demo_mode_enabled = True
            return True
    
    async def validate_demo_mode(self):
        """Validate demo mode functionality"""
        if not self.demo_mode_enabled:
            return True
        
        logger.info("🎭 Validating demo mode functionality...")
        
        try:
            from atlas_demo_mode import run_demo_validation
            success = run_demo_validation()
            
            if success:
                logger.info("✅ Demo mode validation passed")
                return True
            else:
                logger.error("❌ Demo mode validation failed")
                return False
                
        except Exception as e:
            logger.error(f"Demo mode validation error: {e}")
            return False
    
    async def start_core_components(self):
        """Start core A.T.L.A.S. components with error recovery"""
        logger.info("🔧 Starting core A.T.L.A.S. components...")
        
        components = {
            'database_manager': self.start_database_manager,
            'main_server': self.start_main_server,
            'lee_method_scanner': self.start_lee_method_scanner,
            'error_recovery': self.start_error_recovery_monitoring
        }
        
        for component_name, start_func in components.items():
            try:
                logger.info(f"Starting {component_name}...")
                success = await start_func()
                self.components_status[component_name] = 'running' if success else 'failed'
                
                if success:
                    logger.info(f"✅ {component_name} started successfully")
                else:
                    logger.warning(f"⚠️ {component_name} failed to start")
                    
            except Exception as e:
                logger.error(f"❌ Error starting {component_name}: {e}")
                self.components_status[component_name] = 'error'
                
                # Apply graceful degradation if error recovery is available
                if self.error_recovery:
                    degradation = self.error_recovery.graceful_degradation(component_name, e)
                    logger.info(f"Applied degradation: {degradation['fallback_mode']}")
        
        return True
    
    async def start_database_manager(self):
        """Start database manager with error recovery"""
        try:
            from atlas_database_manager import AtlasDatabaseManager
            
            db_manager = AtlasDatabaseManager()
            await db_manager.initialize_databases()
            
            return True
        except Exception as e:
            logger.warning(f"Database manager failed: {e}")
            
            # Fallback to basic database setup
            db_path = self.project_root / "databases"
            if not db_path.exists():
                db_path.mkdir(parents=True, exist_ok=True)
                logger.info("Created databases directory as fallback")
            
            return True  # Continue with basic setup
    
    async def start_main_server(self):
        """Start main A.T.L.A.S. server"""
        try:
            # Check if robust server manager is available
            try:
                from atlas_robust_server_manager import AtlasRobustServerManager
                logger.info("Using robust server manager")
                return True  # Server will be started separately
            except ImportError:
                logger.info("Robust server manager not available, using basic server")
                return True  # Basic server will be started separately
                
        except Exception as e:
            logger.error(f"Server startup preparation failed: {e}")
            return False
    
    async def start_lee_method_scanner(self):
        """Start Lee Method scanner with fallback"""
        try:
            from lee_method_scanner import LeeMethodScanner
            from atlas_lee_method_realtime_scanner import AtlasLeeMethodRealtimeScanner
            
            scanner = LeeMethodScanner()
            realtime_scanner = AtlasLeeMethodRealtimeScanner()
            
            logger.info("Lee Method scanner components loaded")
            return True
            
        except Exception as e:
            logger.warning(f"Lee Method scanner failed: {e}")
            
            if self.demo_mode_enabled:
                logger.info("Lee Method scanner will use demo mode")
                return True
            else:
                return False
    
    async def start_error_recovery_monitoring(self):
        """Start error recovery monitoring"""
        if not self.error_recovery:
            return False
        
        try:
            # Start background monitoring
            logger.info("Error recovery monitoring active")
            return True
        except Exception as e:
            logger.error(f"Error recovery monitoring failed: {e}")
            return False
    
    async def perform_system_health_check(self):
        """Perform comprehensive system health check"""
        logger.info("🏥 Performing system health check...")
        
        health_status = {
            'configuration': self.config_validated,
            'demo_mode': self.demo_mode_enabled,
            'components': self.components_status,
            'error_recovery': self.error_recovery is not None
        }
        
        # Calculate overall health
        component_health = sum(1 for status in self.components_status.values() if status == 'running')
        total_components = len(self.components_status)
        health_percentage = (component_health / total_components * 100) if total_components > 0 else 0
        
        logger.info(f"System health: {health_percentage:.1f}% ({component_health}/{total_components} components running)")
        
        if health_percentage >= 75:
            logger.info("✅ System health: EXCELLENT")
        elif health_percentage >= 50:
            logger.info("⚠️ System health: GOOD (some components degraded)")
        else:
            logger.warning("❌ System health: POOR (multiple component failures)")
        
        return health_status
    
    def show_startup_summary(self, health_status: Dict[str, Any]):
        """Show comprehensive startup summary"""
        print("\n" + "=" * 80)
        print("📊 A.T.L.A.S. STARTUP SUMMARY")
        print("=" * 80)
        
        print(f"\n🔧 Configuration: {'✅ Validated' if health_status['configuration'] else '⚠️ Using defaults'}")
        print(f"🎭 Demo Mode: {'✅ Enabled' if health_status['demo_mode'] else '❌ Disabled'}")
        print(f"🛡️ Error Recovery: {'✅ Active' if health_status['error_recovery'] else '❌ Not available'}")
        
        print(f"\n📦 Component Status:")
        for component, status in health_status['components'].items():
            emoji = "✅" if status == "running" else "⚠️" if status == "failed" else "❌"
            print(f"   {emoji} {component}: {status.upper()}")
        
        print(f"\n🚀 Available Startup Options:")
        print(f"   1. Main Server: python 1_main_chat_engine/atlas_server.py")
        print(f"   2. Robust Server: python 4_helper_tools/atlas_robust_server_manager.py")
        print(f"   3. Lee Method Scanner: python start_lee_method_system.py")
        print(f"   4. Desktop App: cd desktop_app && npm start")
        
        if health_status['demo_mode']:
            print(f"\n🎭 Demo Mode Features:")
            print(f"   - Mock market data and AI responses")
            print(f"   - Lee Method pattern simulation")
            print(f"   - Full interface testing without API keys")
            print(f"   - Run: python 4_helper_tools/atlas_demo_mode.py")
        
        print(f"\n📚 Documentation:")
        print(f"   - README.md - Main project guide")
        print(f"   - LEE_METHOD_README.md - Scanner documentation")
        print(f"   - desktop_app/README.md - Desktop app guide")
        
        print("=" * 80)
    
    async def run_comprehensive_startup(self):
        """Run the complete comprehensive startup sequence"""
        try:
            self.print_startup_banner()
            
            # Initialize error recovery first
            await self.initialize_error_recovery()
            
            # Check and validate configuration
            await self.check_configuration()
            
            # Validate demo mode if enabled
            if self.demo_mode_enabled:
                await self.validate_demo_mode()
            
            # Start core components
            await self.start_core_components()
            
            # Perform health check
            health_status = await self.perform_system_health_check()
            
            # Show summary
            self.show_startup_summary(health_status)
            
            logger.info("🎉 A.T.L.A.S. comprehensive startup completed!")
            return True
            
        except KeyboardInterrupt:
            logger.info("Startup interrupted by user")
            return False
        except Exception as e:
            logger.error(f"Comprehensive startup failed: {e}")
            return False

async def main():
    """Main comprehensive startup function"""
    startup_system = AtlasComprehensiveStartup()
    success = await startup_system.run_comprehensive_startup()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
