#!/usr/bin/env python3
"""
A.T.L.A.S. Emergency Startup Script
Fallback startup mechanism when primary scripts fail
"""

import sys
import os
import logging
import subprocess
import time
from pathlib import Path

# Emergency logging setup
def emergency_log_setup():
    """Emergency logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - EMERGENCY - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

logger = logging.getLogger(__name__)

class AtlasEmergencyStartup:
    """Emergency startup when all else fails"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.emergency_mode = True
        
    def check_python_basics(self):
        """Check if basic Python functionality works"""
        logger.info("Checking basic Python functionality...")
        
        try:
            import json
            import sqlite3
            import http.server
            logger.info("Basic Python modules: OK")
            return True
        except ImportError as e:
            logger.error(f"Basic Python modules missing: {e}")
            return False
    
    def find_atlas_components(self):
        """Find available A.T.L.A.S. components"""
        logger.info("Scanning for A.T.L.A.S. components...")
        
        components = {}
        
        # Check for main server
        main_server = self.project_root / "1_main_chat_engine" / "atlas_server.py"
        if main_server.exists():
            components['main_server'] = main_server
            logger.info("Found: Main A.T.L.A.S. server")
        
        # Check for Lee Method scanner
        lee_scanner = self.project_root / "lee_method_scanner.py"
        if lee_scanner.exists():
            components['lee_scanner'] = lee_scanner
            logger.info("Found: Lee Method scanner")
        
        # Check for databases
        db_dir = self.project_root / "databases"
        if db_dir.exists():
            components['databases'] = db_dir
            logger.info("Found: Database directory")
        
        # Check for desktop app
        desktop_app = self.project_root / "desktop_app"
        if desktop_app.exists():
            components['desktop_app'] = desktop_app
            logger.info("Found: Desktop application")
        
        return components
    
    def try_simple_server_start(self):
        """Try to start server with minimal configuration"""
        logger.info("Attempting simple server startup...")
        
        try:
            # Try to start with basic uvicorn
            main_server = self.project_root / "1_main_chat_engine" / "atlas_server.py"
            if main_server.exists():
                cmd = [sys.executable, str(main_server)]
                logger.info(f"Executing: {' '.join(cmd)}")
                
                process = subprocess.Popen(
                    cmd,
                    cwd=str(self.project_root),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # Wait a moment to see if it starts
                time.sleep(3)
                
                if process.poll() is None:
                    logger.info("Server appears to be starting...")
                    return process
                else:
                    stdout, stderr = process.communicate()
                    logger.error(f"Server failed to start:")
                    logger.error(f"STDOUT: {stdout}")
                    logger.error(f"STDERR: {stderr}")
                    return None
            
        except Exception as e:
            logger.error(f"Failed to start simple server: {e}")
            return None
    
    def create_emergency_html(self):
        """Create emergency HTML interface"""
        logger.info("Creating emergency HTML interface...")
        
        emergency_html = """<!DOCTYPE html>
<html>
<head>
    <title>A.T.L.A.S. Emergency Mode</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { color: #d32f2f; text-align: center; margin-bottom: 30px; }
        .status { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .instructions { background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 A.T.L.A.S. Emergency Mode</h1>
            <p>Advanced Trading & Learning Analysis System</p>
        </div>
        
        <div class="status">
            <h3>System Status</h3>
            <p>A.T.L.A.S. is running in emergency mode. Some features may be limited.</p>
        </div>
        
        <div class="instructions">
            <h3>Recovery Instructions</h3>
            <p>To restore full functionality:</p>
            <ol>
                <li>Check that all dependencies are installed: <code class="code">pip install -r requirements.txt</code></li>
                <li>Try the minimal startup: <code class="code">python minimal_atlas_startup.py</code></li>
                <li>If that fails, try the robust server: <code class="code">python 4_helper_tools/atlas_robust_server_manager.py</code></li>
                <li>For desktop app: <code class="code">cd desktop_app && npm start</code></li>
            </ol>
        </div>
        
        <div class="status">
            <h3>Available Components</h3>
            <p>Emergency mode detected the following components:</p>
            <ul id="components">
                <li>Emergency interface (this page)</li>
            </ul>
        </div>
    </div>
</body>
</html>"""
        
        try:
            emergency_file = self.project_root / "emergency_interface.html"
            with open(emergency_file, 'w', encoding='utf-8') as f:
                f.write(emergency_html)
            logger.info(f"Emergency interface created: {emergency_file}")
            return emergency_file
        except Exception as e:
            logger.error(f"Failed to create emergency interface: {e}")
            return None
    
    def start_emergency_server(self):
        """Start emergency HTTP server"""
        logger.info("Starting emergency HTTP server...")
        
        try:
            import http.server
            import socketserver
            import threading
            
            # Create emergency interface
            emergency_file = self.create_emergency_html()
            if not emergency_file:
                return False
            
            # Simple HTTP server
            class EmergencyHandler(http.server.SimpleHTTPRequestHandler):
                def do_GET(self):
                    if self.path == '/' or self.path == '/index.html':
                        self.path = '/emergency_interface.html'
                    return super().do_GET()
            
            # Find available port
            for port in range(8080, 8090):
                try:
                    with socketserver.TCPServer(("", port), EmergencyHandler) as httpd:
                        logger.info(f"Emergency server starting on http://localhost:{port}")
                        
                        # Start server in background thread
                        server_thread = threading.Thread(target=httpd.serve_forever)
                        server_thread.daemon = True
                        server_thread.start()
                        
                        logger.info("Emergency server is running")
                        logger.info("Press Ctrl+C to stop")
                        
                        # Keep main thread alive
                        try:
                            while True:
                                time.sleep(1)
                        except KeyboardInterrupt:
                            logger.info("Emergency server stopped")
                            httpd.shutdown()
                            return True
                        
                except OSError:
                    continue  # Try next port
            
            logger.error("No available ports for emergency server")
            return False
            
        except Exception as e:
            logger.error(f"Failed to start emergency server: {e}")
            return False
    
    def run_emergency_startup(self):
        """Run emergency startup sequence"""
        logger.info("=" * 50)
        logger.info("A.T.L.A.S. EMERGENCY STARTUP")
        logger.info("=" * 50)
        
        # Check basics
        if not self.check_python_basics():
            logger.error("Basic Python functionality check failed")
            return False
        
        # Find components
        components = self.find_atlas_components()
        if not components:
            logger.warning("No A.T.L.A.S. components found")
        
        # Try simple server start first
        server_process = self.try_simple_server_start()
        if server_process:
            logger.info("Simple server startup successful")
            try:
                server_process.wait()
            except KeyboardInterrupt:
                logger.info("Server stopped by user")
                server_process.terminate()
            return True
        
        # Fall back to emergency server
        logger.info("Falling back to emergency server...")
        return self.start_emergency_server()

def main():
    """Emergency startup main function"""
    emergency_log_setup()
    
    try:
        emergency_startup = AtlasEmergencyStartup()
        success = emergency_startup.run_emergency_startup()
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"Emergency startup failed: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
