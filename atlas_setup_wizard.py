#!/usr/bin/env python3
"""
A.T.L.A.S. Interactive Setup Wizard
Automated configuration and API key detection for reorganized project structure
"""

import sys
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import getpass

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AtlasSetupWizard:
    """Interactive setup wizard for A.T.L.A.S. configuration"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_path = self.project_root / "4_helper_tools" / "config.py"
        self.env_path = self.project_root / ".env"
        self.config_data = {}
        self.detected_apis = {}
        
    def print_banner(self):
        """Print setup wizard banner"""
        print("=" * 70)
        print("🚀 A.T.L.A.S. INTERACTIVE SETUP WIZARD")
        print("   Advanced Trading & Learning Analysis System")
        print("=" * 70)
        print()
        print("This wizard will help you configure A.T.L.A.S. for first-time use.")
        print("We'll detect available API keys and set up your configuration.")
        print()
    
    def detect_environment_variables(self):
        """Detect API keys from environment variables"""
        print("🔍 Detecting API keys from environment variables...")
        
        api_keys = {
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
            'INCITE_API_KEY': os.getenv('INCITE_API_KEY'),
            'ALPHA_VANTAGE_API_KEY': os.getenv('ALPHA_VANTAGE_API_KEY'),
            'POLYGON_API_KEY': os.getenv('POLYGON_API_KEY'),
            'FINNHUB_API_KEY': os.getenv('FINNHUB_API_KEY')
        }
        
        found_keys = {k: v for k, v in api_keys.items() if v}
        
        if found_keys:
            print("✅ Found API keys in environment:")
            for key in found_keys:
                masked_key = found_keys[key][:8] + "..." if len(found_keys[key]) > 8 else found_keys[key]
                print(f"   - {key}: {masked_key}")
            self.detected_apis.update(found_keys)
        else:
            print("⚠️  No API keys found in environment variables")
        
        print()
        return found_keys
    
    def check_existing_config(self):
        """Check for existing configuration files"""
        print("📁 Checking for existing configuration...")
        
        existing_configs = []
        
        # Check config.py
        if self.config_path.exists():
            existing_configs.append("config.py")
            print(f"✅ Found existing config: {self.config_path}")
        
        # Check .env file
        if self.env_path.exists():
            existing_configs.append(".env")
            print(f"✅ Found existing .env: {self.env_path}")
        
        if existing_configs:
            response = input("\n🤔 Existing configuration found. Overwrite? (y/N): ").strip().lower()
            if response != 'y':
                print("Setup cancelled. Existing configuration preserved.")
                return False
        
        print()
        return True
    
    def interactive_api_setup(self):
        """Interactive API key setup"""
        print("🔑 API Key Configuration")
        print("=" * 40)
        
        api_configs = {
            'OPENAI_API_KEY': {
                'name': 'OpenAI API Key',
                'description': 'Required for AI chat functionality',
                'required': True,
                'url': 'https://platform.openai.com/api-keys'
            },
            'INCITE_API_KEY': {
                'name': 'Incite API Key',
                'description': 'For advanced market data (optional)',
                'required': False,
                'url': 'https://inciteapi.com'
            },
            'ALPHA_VANTAGE_API_KEY': {
                'name': 'Alpha Vantage API Key',
                'description': 'For stock market data (optional)',
                'required': False,
                'url': 'https://www.alphavantage.co/support/#api-key'
            }
        }
        
        for key, config in api_configs.items():
            print(f"\n📊 {config['name']}")
            print(f"   Description: {config['description']}")
            print(f"   Get it at: {config['url']}")
            
            # Check if already detected
            if key in self.detected_apis:
                use_detected = input(f"   Use detected key? (Y/n): ").strip().lower()
                if use_detected != 'n':
                    self.config_data[key] = self.detected_apis[key]
                    print("   ✅ Using detected API key")
                    continue
            
            # Manual entry
            if config['required']:
                while True:
                    api_key = getpass.getpass(f"   Enter {config['name']} (required): ").strip()
                    if api_key:
                        self.config_data[key] = api_key
                        break
                    print("   ❌ This API key is required!")
            else:
                api_key = getpass.getpass(f"   Enter {config['name']} (optional, press Enter to skip): ").strip()
                if api_key:
                    self.config_data[key] = api_key
                    print("   ✅ API key saved")
                else:
                    print("   ⏭️  Skipped")
    
    def configure_system_settings(self):
        """Configure system settings"""
        print("\n⚙️  System Configuration")
        print("=" * 40)
        
        # Server settings
        print("\n🌐 Server Settings:")
        host = input("   Server host (default: localhost): ").strip() or "localhost"
        port = input("   Server port (default: 8080): ").strip() or "8080"
        
        try:
            port = int(port)
        except ValueError:
            port = 8080
            print("   ⚠️  Invalid port, using default: 8080")
        
        self.config_data['SERVER_HOST'] = host
        self.config_data['SERVER_PORT'] = port
        
        # Database settings
        print("\n💾 Database Settings:")
        db_path = input("   Database directory (default: databases/): ").strip() or "databases/"
        self.config_data['DATABASE_PATH'] = db_path
        
        # Lee Method settings
        print("\n📊 Lee Method Scanner Settings:")
        scan_interval = input("   Scan interval in seconds (default: 300): ").strip() or "300"
        
        try:
            scan_interval = int(scan_interval)
        except ValueError:
            scan_interval = 300
            print("   ⚠️  Invalid interval, using default: 300 seconds")
        
        self.config_data['LEE_METHOD_SCAN_INTERVAL'] = scan_interval
        
        # Demo mode
        print("\n🎭 Demo Mode:")
        demo_mode = input("   Enable demo mode for testing? (y/N): ").strip().lower() == 'y'
        self.config_data['DEMO_MODE'] = demo_mode
        
        if demo_mode:
            print("   ✅ Demo mode enabled - system will work with mock data")
        else:
            print("   ✅ Production mode - real API calls will be made")
    
    def generate_config_file(self):
        """Generate config.py file"""
        print("\n📝 Generating configuration file...")
        
        config_content = f'''#!/usr/bin/env python3
"""
A.T.L.A.S. Configuration File
Generated by Setup Wizard
"""

import os
from pathlib import Path

# Project structure paths
PROJECT_ROOT = Path(__file__).parent.parent
DATABASE_PATH = PROJECT_ROOT / "{self.config_data.get('DATABASE_PATH', 'databases/')}"

# Server configuration
SERVER_HOST = "{self.config_data.get('SERVER_HOST', 'localhost')}"
SERVER_PORT = {self.config_data.get('SERVER_PORT', 8080)}

# API Keys
OPENAI_API_KEY = "{self.config_data.get('OPENAI_API_KEY', '')}"
INCITE_API_KEY = "{self.config_data.get('INCITE_API_KEY', '')}"
ALPHA_VANTAGE_API_KEY = "{self.config_data.get('ALPHA_VANTAGE_API_KEY', '')}"

# Lee Method Scanner Settings
LEE_METHOD_SCAN_INTERVAL = {self.config_data.get('LEE_METHOD_SCAN_INTERVAL', 300)}

# System Settings
DEMO_MODE = {self.config_data.get('DEMO_MODE', False)}
DEBUG_MODE = False
LOG_LEVEL = "INFO"

# Database configuration
DATABASES = {{
    'main': DATABASE_PATH / 'atlas.db',
    'memory': DATABASE_PATH / 'atlas_memory.db',
    'rag': DATABASE_PATH / 'atlas_rag.db',
    'compliance': DATABASE_PATH / 'atlas_compliance.db',
    'feedback': DATABASE_PATH / 'atlas_feedback.db',
    'enhanced_memory': DATABASE_PATH / 'atlas_enhanced_memory.db',
    'auto_trading': DATABASE_PATH / 'atlas_auto_trading.db'
}}

# Trading settings
TRADING_ENABLED = not DEMO_MODE
RISK_MANAGEMENT_ENABLED = True
MAX_POSITION_SIZE = 0.02  # 2% of portfolio per position

# Lee Method specific settings
LEE_METHOD_CONFIG = {{
    'min_histogram_bars': 3,
    'momentum_threshold': 0.1,
    'confidence_threshold': 0.7,
    'timeframes': ['1d', '1w'],
    'symbols_to_scan': ['SPY', 'QQQ', 'IWM', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
}}

# Validation functions
def validate_config():
    """Validate configuration settings"""
    errors = []
    
    if not OPENAI_API_KEY and not DEMO_MODE:
        errors.append("OpenAI API key is required for production mode")
    
    if not DATABASE_PATH.exists():
        try:
            DATABASE_PATH.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            errors.append(f"Cannot create database directory: {{e}}")
    
    return errors

def get_api_key(service: str) -> str:
    """Get API key for a service with fallback to environment"""
    key_map = {{
        'openai': OPENAI_API_KEY,
        'incite': INCITE_API_KEY,
        'alpha_vantage': ALPHA_VANTAGE_API_KEY
    }}
    
    # Try config first, then environment
    key = key_map.get(service.lower(), '')
    if not key:
        env_key = f"{{service.upper()}}_API_KEY"
        key = os.getenv(env_key, '')
    
    return key

# Auto-validate on import
if __name__ != "__main__":
    validation_errors = validate_config()
    if validation_errors:
        import logging
        logger = logging.getLogger(__name__)
        for error in validation_errors:
            logger.warning(f"Config validation: {{error}}")
'''
        
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print(f"✅ Configuration saved to: {self.config_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to save configuration: {e}")
            return False
    
    def generate_env_file(self):
        """Generate .env file for environment variables"""
        print("📄 Generating .env file...")
        
        env_content = f"""# A.T.L.A.S. Environment Variables
# Generated by Setup Wizard

# API Keys
OPENAI_API_KEY={self.config_data.get('OPENAI_API_KEY', '')}
INCITE_API_KEY={self.config_data.get('INCITE_API_KEY', '')}
ALPHA_VANTAGE_API_KEY={self.config_data.get('ALPHA_VANTAGE_API_KEY', '')}

# Server Settings
SERVER_HOST={self.config_data.get('SERVER_HOST', 'localhost')}
SERVER_PORT={self.config_data.get('SERVER_PORT', 8080)}

# System Settings
DEMO_MODE={self.config_data.get('DEMO_MODE', False)}
LEE_METHOD_SCAN_INTERVAL={self.config_data.get('LEE_METHOD_SCAN_INTERVAL', 300)}
"""
        
        try:
            with open(self.env_path, 'w', encoding='utf-8') as f:
                f.write(env_content)
            print(f"✅ Environment file saved to: {self.env_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to save .env file: {e}")
            return False
    
    def show_next_steps(self):
        """Show next steps after setup"""
        print("\n🎉 Setup Complete!")
        print("=" * 40)
        print("\n📋 Next Steps:")
        print("1. Start A.T.L.A.S. system:")
        print("   python minimal_atlas_startup.py")
        print("\n2. Or start the main server directly:")
        print("   python 1_main_chat_engine/atlas_server.py")
        print("\n3. Launch desktop application:")
        print("   cd desktop_app && npm start")
        print("\n4. Access web interface:")
        print(f"   http://{self.config_data.get('SERVER_HOST', 'localhost')}:{self.config_data.get('SERVER_PORT', 8080)}")
        
        if self.config_data.get('DEMO_MODE'):
            print("\n🎭 Demo Mode Active:")
            print("   - System will use mock data for testing")
            print("   - No real API calls will be made")
            print("   - Perfect for trying out A.T.L.A.S. features")
        
        print("\n📚 Documentation:")
        print("   - README.md - Main project documentation")
        print("   - LEE_METHOD_README.md - Lee Method scanner guide")
        print("   - desktop_app/README.md - Desktop app instructions")
        print()
    
    def run_setup(self):
        """Run the complete setup wizard"""
        try:
            self.print_banner()
            
            # Check existing config
            if not self.check_existing_config():
                return False
            
            # Detect environment
            self.detect_environment_variables()
            
            # Interactive setup
            self.interactive_api_setup()
            self.configure_system_settings()
            
            # Generate files
            config_success = self.generate_config_file()
            env_success = self.generate_env_file()
            
            if config_success and env_success:
                self.show_next_steps()
                return True
            else:
                print("❌ Setup failed. Please check the errors above.")
                return False
                
        except KeyboardInterrupt:
            print("\n\n⚠️  Setup cancelled by user.")
            return False
        except Exception as e:
            print(f"\n❌ Setup failed with error: {e}")
            return False

def main():
    """Main setup function"""
    wizard = AtlasSetupWizard()
    success = wizard.run_setup()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
