#!/usr/bin/env python3
"""
A.T.L.A.S. System Integration Script
Integrates all enhanced components and validates complete system functionality
"""

import sys
import os
import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, Any, List

# Add project paths
project_root = Path(__file__).parent
sys.path.extend([
    str(project_root),
    str(project_root / "4_helper_tools")
])

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AtlasSystemIntegration:
    """Complete system integration and validation"""
    
    def __init__(self):
        self.project_root = project_root
        self.integration_results = {}
        self.all_systems_operational = False
        
    def print_integration_banner(self):
        """Print system integration banner"""
        print("=" * 80)
        print("🔗 A.T.L.A.S. SYSTEM INTEGRATION & VALIDATION")
        print("   Advanced Trading & Learning Analysis System")
        print("   Complete System Integration Test")
        print("=" * 80)
        print()
    
    async def test_startup_systems(self):
        """Test all startup systems"""
        print("🚀 Testing Startup Systems...")
        
        startup_tests = {
            'minimal_startup': self.test_minimal_startup,
            'comprehensive_startup': self.test_comprehensive_startup,
            'emergency_startup': self.test_emergency_startup,
            'robust_server_manager': self.test_robust_server_manager
        }
        
        results = {}
        for test_name, test_func in startup_tests.items():
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {status} {test_name}")
            except Exception as e:
                results[test_name] = False
                print(f"  ❌ FAIL {test_name}: {e}")
        
        self.integration_results['startup_systems'] = results
        return all(results.values())
    
    async def test_minimal_startup(self):
        """Test minimal startup script"""
        try:
            from minimal_atlas_startup import AtlasMinimalStartup
            startup = AtlasMinimalStartup()
            return startup.validate_project_structure()
        except ImportError:
            return False
    
    async def test_comprehensive_startup(self):
        """Test comprehensive startup script"""
        try:
            from atlas_comprehensive_startup import AtlasComprehensiveStartup
            startup = AtlasComprehensiveStartup()
            return True  # Just test import for now
        except ImportError:
            return False
    
    async def test_emergency_startup(self):
        """Test emergency startup script"""
        try:
            from atlas_emergency_startup import AtlasEmergencyStartup
            startup = AtlasEmergencyStartup()
            return startup.check_python_basics()
        except ImportError:
            return False
    
    async def test_robust_server_manager(self):
        """Test robust server manager"""
        try:
            from atlas_robust_server_manager import AtlasRobustServerManager
            manager = AtlasRobustServerManager()
            return manager.validate_server_environment()
        except ImportError:
            return False
    
    async def test_configuration_systems(self):
        """Test configuration and setup systems"""
        print("\n🔧 Testing Configuration Systems...")
        
        config_tests = {
            'setup_wizard': self.test_setup_wizard,
            'config_validation': self.test_config_validation,
            'demo_mode': self.test_demo_mode
        }
        
        results = {}
        for test_name, test_func in config_tests.items():
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {status} {test_name}")
            except Exception as e:
                results[test_name] = False
                print(f"  ❌ FAIL {test_name}: {e}")
        
        self.integration_results['configuration_systems'] = results
        return all(results.values())
    
    async def test_setup_wizard(self):
        """Test setup wizard"""
        try:
            from atlas_setup_wizard import AtlasSetupWizard
            return True  # Just test import
        except ImportError:
            return False
    
    async def test_config_validation(self):
        """Test configuration validation"""
        try:
            config_file = self.project_root / "4_helper_tools" / "config.py"
            return config_file.exists()
        except Exception:
            return False
    
    async def test_demo_mode(self):
        """Test demo mode functionality"""
        try:
            from atlas_demo_mode import AtlasDemoMode, run_demo_validation
            demo = AtlasDemoMode()
            validation_results = demo.validate_demo_functionality()
            return all(validation_results.values())
        except ImportError:
            return False
    
    async def test_error_recovery_systems(self):
        """Test error recovery and resilience systems"""
        print("\n🛡️ Testing Error Recovery Systems...")
        
        recovery_tests = {
            'error_recovery': self.test_error_recovery,
            'graceful_degradation': self.test_graceful_degradation,
            'health_monitoring': self.test_health_monitoring
        }
        
        results = {}
        for test_name, test_func in recovery_tests.items():
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {status} {test_name}")
            except Exception as e:
                results[test_name] = False
                print(f"  ❌ FAIL {test_name}: {e}")
        
        self.integration_results['error_recovery_systems'] = results
        return all(results.values())
    
    async def test_error_recovery(self):
        """Test error recovery system"""
        try:
            from atlas_error_recovery import AtlasErrorRecovery
            recovery = AtlasErrorRecovery()
            return True  # Just test import and initialization
        except ImportError:
            return False
    
    async def test_graceful_degradation(self):
        """Test graceful degradation system"""
        try:
            from atlas_graceful_degradation import AtlasGracefulDegradation
            degradation = AtlasGracefulDegradation()
            report = degradation.get_system_degradation_report()
            return 'overall_status' in report
        except ImportError:
            return False
    
    async def test_health_monitoring(self):
        """Test health monitoring system"""
        try:
            from atlas_health_monitor import AtlasHealthMonitor
            monitor = AtlasHealthMonitor()
            return True  # Just test import
        except ImportError:
            return False
    
    async def test_logging_systems(self):
        """Test enhanced logging systems"""
        print("\n📝 Testing Logging Systems...")
        
        logging_tests = {
            'enhanced_logging': self.test_enhanced_logging,
            'log_aggregation': self.test_log_aggregation,
            'performance_metrics': self.test_performance_metrics
        }
        
        results = {}
        for test_name, test_func in logging_tests.items():
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {status} {test_name}")
            except Exception as e:
                results[test_name] = False
                print(f"  ❌ FAIL {test_name}: {e}")
        
        self.integration_results['logging_systems'] = results
        return all(results.values())
    
    async def test_enhanced_logging(self):
        """Test enhanced logging system"""
        try:
            from atlas_enhanced_logging import AtlasLogger, get_atlas_logger
            logger = get_atlas_logger()
            return logger is not None
        except ImportError:
            return False
    
    async def test_log_aggregation(self):
        """Test log aggregation"""
        try:
            from atlas_enhanced_logging import get_atlas_logger
            logger = get_atlas_logger()
            logs = logger.get_log_aggregation(limit=1)
            return isinstance(logs, list)
        except Exception:
            return False
    
    async def test_performance_metrics(self):
        """Test performance metrics"""
        try:
            from atlas_enhanced_logging import get_atlas_logger
            logger = get_atlas_logger()
            report = logger.get_performance_report()
            return isinstance(report, dict)
        except Exception:
            return False
    
    async def test_desktop_application(self):
        """Test desktop application"""
        print("\n🖥️ Testing Desktop Application...")
        
        desktop_tests = {
            'desktop_interface': self.test_desktop_interface,
            'electron_config': self.test_electron_config,
            'node_dependencies': self.test_node_dependencies
        }
        
        results = {}
        for test_name, test_func in desktop_tests.items():
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"  {status} {test_name}")
            except Exception as e:
                results[test_name] = False
                print(f"  ❌ FAIL {test_name}: {e}")
        
        self.integration_results['desktop_application'] = results
        return any(results.values())  # Desktop app is optional
    
    async def test_desktop_interface(self):
        """Test desktop interface file"""
        interface_file = self.project_root / "desktop_app" / "atlas_desktop_interface.html"
        return interface_file.exists()
    
    async def test_electron_config(self):
        """Test Electron configuration"""
        main_js = self.project_root / "desktop_app" / "main.js"
        package_json = self.project_root / "desktop_app" / "package.json"
        return main_js.exists() and package_json.exists()
    
    async def test_node_dependencies(self):
        """Test Node.js dependencies"""
        node_modules = self.project_root / "desktop_app" / "node_modules"
        return node_modules.exists()
    
    async def test_project_structure(self):
        """Test reorganized project structure"""
        print("\n📁 Testing Project Structure...")
        
        required_dirs = [
            "1_main_chat_engine",
            "2_trading_logic", 
            "3_market_news_data",
            "4_helper_tools",
            "databases",
            "desktop_app",
            "tests"
        ]
        
        missing_dirs = []
        for directory in required_dirs:
            dir_path = self.project_root / directory
            if not dir_path.exists():
                missing_dirs.append(directory)
            else:
                print(f"  ✅ {directory}/")
        
        if missing_dirs:
            print(f"  ❌ Missing directories: {missing_dirs}")
            return False
        
        self.integration_results['project_structure'] = True
        return True
    
    def generate_integration_report(self):
        """Generate comprehensive integration report"""
        print("\n" + "=" * 80)
        print("📊 A.T.L.A.S. SYSTEM INTEGRATION REPORT")
        print("=" * 80)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.integration_results.items():
            print(f"\n📦 {category.replace('_', ' ').title()}:")
            
            if isinstance(tests, dict):
                for test_name, result in tests.items():
                    total_tests += 1
                    if result:
                        passed_tests += 1
                        print(f"  ✅ {test_name}")
                    else:
                        print(f"  ❌ {test_name}")
            else:
                total_tests += 1
                if tests:
                    passed_tests += 1
                    print(f"  ✅ {category}")
                else:
                    print(f"  ❌ {category}")
        
        # Calculate overall score
        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
        else:
            success_rate = 0
        
        print(f"\n📈 Overall Integration Score: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        if success_rate >= 90:
            print("🎉 EXCELLENT - A.T.L.A.S. system is fully integrated and ready for production!")
            self.all_systems_operational = True
        elif success_rate >= 75:
            print("✅ GOOD - A.T.L.A.S. system is well integrated with minor issues")
        elif success_rate >= 50:
            print("⚠️ FAIR - A.T.L.A.S. system has some integration issues")
        else:
            print("❌ POOR - A.T.L.A.S. system has significant integration problems")
        
        print("=" * 80)
        
        return success_rate
    
    async def run_complete_integration_test(self):
        """Run complete system integration test"""
        self.print_integration_banner()
        
        # Run all test categories
        test_categories = [
            self.test_project_structure(),
            self.test_startup_systems(),
            self.test_configuration_systems(),
            self.test_error_recovery_systems(),
            self.test_logging_systems(),
            self.test_desktop_application()
        ]
        
        # Execute all tests
        results = await asyncio.gather(*test_categories, return_exceptions=True)
        
        # Generate report
        success_rate = self.generate_integration_report()
        
        return success_rate >= 75  # Consider 75%+ as successful integration

async def main():
    """Main integration test function"""
    integration = AtlasSystemIntegration()
    success = await integration.run_complete_integration_test()
    
    if success:
        print("\n🚀 A.T.L.A.S. system integration completed successfully!")
        print("The system is ready for production use.")
        return 0
    else:
        print("\n⚠️ A.T.L.A.S. system integration completed with issues.")
        print("Review the report above and address any failed tests.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
