<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. Desktop - Lee Method Trading Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #0f1419;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Desktop App Header */
        .desktop-header {
            background: linear-gradient(90deg, #1a1f2e 0%, #2d3748 100%);
            padding: 12px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #2d3748;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .app-title {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .app-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #4299e1, #63b3ed);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .app-title h1 {
            font-size: 18px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .app-subtitle {
            font-size: 12px;
            color: #a0aec0;
            margin-left: 4px;
        }

        .status-indicators {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #48bb78;
            animation: pulse 2s infinite;
        }

        .status-text {
            font-size: 12px;
            color: #a0aec0;
        }

        /* Main Layout */
        .desktop-main {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 400px;
            grid-template-rows: 1fr 300px;
            gap: 1px;
            background: #2d3748;
        }

        /* Chat Panel */
        .chat-section {
            background: #1a202c;
            display: flex;
            flex-direction: column;
            border-radius: 8px 0 0 0;
        }

        .chat-header {
            background: #2d3748;
            padding: 16px 20px;
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .chat-title {
            font-size: 16px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #1a202c;
        }

        .message {
            margin-bottom: 16px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            margin-left: auto;
            color: white;
        }

        .message.assistant {
            background: #2d3748;
            border: 1px solid #4a5568;
            color: #e2e8f0;
        }

        .chat-input-area {
            padding: 20px;
            background: #2d3748;
            border-top: 1px solid #4a5568;
        }

        .chat-input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            background: #1a202c;
            border: 1px solid #4a5568;
            border-radius: 12px;
            padding: 12px 16px;
            color: #e2e8f0;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }

        .chat-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .send-button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .send-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        }

        /* Lee Method Scanner Panel */
        .scanner-panel {
            background: #1a202c;
            display: flex;
            flex-direction: column;
            border-radius: 0 8px 0 0;
        }

        .scanner-header {
            background: #2d3748;
            padding: 16px 20px;
            border-bottom: 1px solid #4a5568;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .scanner-title {
            font-size: 16px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .scan-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #a0aec0;
        }

        .scanner-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .scan-result {
            background: #2d3748;
            border: 1px solid #4a5568;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }

        .scan-result.signal {
            border-color: #48bb78;
            background: rgba(72, 187, 120, 0.1);
        }

        .scan-symbol {
            font-weight: 600;
            font-size: 16px;
            color: #e2e8f0;
            margin-bottom: 8px;
        }

        .scan-details {
            font-size: 12px;
            color: #a0aec0;
            line-height: 1.4;
        }

        /* Analysis Panel */
        .analysis-section {
            background: #1a202c;
            display: flex;
            flex-direction: column;
            border-radius: 0 0 0 8px;
            grid-column: 1 / 2;
        }

        .analysis-header {
            background: #2d3748;
            padding: 16px 20px;
            border-bottom: 1px solid #4a5568;
        }

        .analysis-title {
            font-size: 16px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .analysis-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .analysis-point {
            background: #2d3748;
            border-left: 4px solid #4299e1;
            padding: 12px 16px;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
        }

        .point-number {
            font-weight: 600;
            color: #4299e1;
            font-size: 14px;
        }

        .point-content {
            color: #e2e8f0;
            font-size: 14px;
            margin-top: 4px;
        }

        /* Market Data Panel */
        .market-panel {
            background: #1a202c;
            display: flex;
            flex-direction: column;
            border-radius: 0 0 8px 0;
        }

        .market-header {
            background: #2d3748;
            padding: 16px 20px;
            border-bottom: 1px solid #4a5568;
        }

        .market-title {
            font-size: 16px;
            font-weight: 600;
            color: #e2e8f0;
        }

        .market-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .market-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #2d3748;
        }

        .market-symbol {
            font-weight: 600;
            color: #e2e8f0;
        }

        .market-price {
            color: #48bb78;
            font-weight: 600;
        }

        .market-change {
            font-size: 12px;
            color: #a0aec0;
        }

        /* Animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #2d3748;
        }

        ::-webkit-scrollbar-thumb {
            background: #4a5568;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #718096;
        }
    </style>
</head>
<body>
    <!-- Desktop Header -->
    <div class="desktop-header">
        <div class="app-title">
            <div class="app-logo">A</div>
            <div>
                <h1>A.T.L.A.S. Desktop</h1>
                <div class="app-subtitle">Lee Method Trading Assistant</div>
            </div>
        </div>
        <div class="status-indicators">
            <div class="status-dot"></div>
            <span class="status-text">Connected to localhost:8081</span>
        </div>
    </div>

    <!-- Main Desktop Layout -->
    <div class="desktop-main">
        <!-- Chat Section -->
        <div class="chat-section">
            <div class="chat-header">
                <div class="chat-title">💬 AI Trading Assistant</div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <strong>A.T.L.A.S. Trading Assistant</strong><br>
                    Welcome to your desktop trading assistant! I'm here to help you with Lee Method pattern analysis, trading strategies, and market insights. How can I assist you today?
                </div>
            </div>
            <div class="chat-input-area">
                <div class="chat-input-container">
                    <textarea class="chat-input" id="chatInput" placeholder="Ask me about Lee Method patterns, trading strategies, or market analysis..." rows="1"></textarea>
                    <button class="send-button" id="sendButton">Send</button>
                </div>
            </div>
        </div>

        <!-- Lee Method Scanner Panel -->
        <div class="scanner-panel">
            <div class="scanner-header">
                <div class="scanner-title">📊 Lee Method Scanner</div>
                <div class="scan-status">
                    <div class="status-dot"></div>
                    <span>Scanning...</span>
                </div>
            </div>
            <div class="scanner-content" id="scannerResults">
                <div class="scan-result">
                    <div class="scan-symbol">Scanner Initializing...</div>
                    <div class="scan-details">Connecting to Lee Method scanner system...</div>
                </div>
            </div>
        </div>

        <!-- 6-Point Analysis Section -->
        <div class="analysis-section">
            <div class="analysis-header">
                <div class="analysis-title">🎯 6-Point Analysis</div>
            </div>
            <div class="analysis-content" id="analysisContent">
                <div class="analysis-point">
                    <div class="point-number">Ready for Analysis</div>
                    <div class="point-content">Ask me to analyze any symbol for detailed 6-point trading analysis</div>
                </div>
            </div>
        </div>

        <!-- Market Data Panel -->
        <div class="market-panel">
            <div class="market-header">
                <div class="market-title">📈 Market Overview</div>
            </div>
            <div class="market-content" id="marketData">
                <div class="market-item">
                    <span class="market-symbol">Loading market data...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Desktop App JavaScript
        const API_BASE = 'http://localhost:8081';
        let isConnected = false;

        // Initialize desktop app
        document.addEventListener('DOMContentLoaded', function() {
            initializeDesktopApp();
            setupEventListeners();
            startLeeMethodScanner();
            loadMarketData();
        });

        function initializeDesktopApp() {
            console.log('🚀 A.T.L.A.S. Desktop App Initialized');
            checkServerConnection();
        }

        function setupEventListeners() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');

            // Send message on button click
            sendButton.addEventListener('click', sendMessage);

            // Send message on Enter (but allow Shift+Enter for new lines)
            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Auto-resize textarea
            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        async function checkServerConnection() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    isConnected = true;
                    updateConnectionStatus('Connected', true);
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                isConnected = false;
                updateConnectionStatus('Disconnected', false);
                console.error('Connection error:', error);
            }
        }

        function updateConnectionStatus(status, connected) {
            const statusText = document.querySelector('.status-text');
            const statusDot = document.querySelector('.status-dot');
            
            statusText.textContent = `${status} - localhost:8081`;
            statusDot.style.background = connected ? '#48bb78' : '#f56565';
        }

        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();
            
            if (!message) return;

            // Add user message to chat
            addMessageToChat(message, 'user');
            chatInput.value = '';
            chatInput.style.height = 'auto';

            try {
                // Send to A.T.L.A.S. API
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                if (response.ok) {
                    const data = await response.json();
                    addMessageToChat(data.response, 'assistant');
                    
                    // Update analysis if provided
                    if (data.analysis) {
                        updateAnalysisPanel(data.analysis);
                    }
                } else {
                    throw new Error('Failed to get response');
                }
            } catch (error) {
                addMessageToChat('Sorry, I\'m having trouble connecting to the server. Please make sure the A.T.L.A.S. server is running on localhost:8081.', 'assistant');
                console.error('Chat error:', error);
            }
        }

        function addMessageToChat(message, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            if (sender === 'assistant') {
                messageDiv.innerHTML = `<strong>A.T.L.A.S.</strong><br>${message}`;
            } else {
                messageDiv.textContent = message;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        async function startLeeMethodScanner() {
            try {
                const response = await fetch(`${API_BASE}/lee_method/scan`);
                if (response.ok) {
                    const data = await response.json();
                    updateScannerResults(data.results || []);
                }
            } catch (error) {
                console.error('Scanner error:', error);
                updateScannerResults([]);
            }

            // Refresh scanner every 30 seconds
            setTimeout(startLeeMethodScanner, 30000);
        }

        function updateScannerResults(results) {
            const scannerContent = document.getElementById('scannerResults');
            
            if (results.length === 0) {
                scannerContent.innerHTML = `
                    <div class="scan-result">
                        <div class="scan-symbol">No Signals</div>
                        <div class="scan-details">No Lee Method patterns detected at this time</div>
                    </div>
                `;
                return;
            }

            scannerContent.innerHTML = results.map(result => `
                <div class="scan-result ${result.signal_type ? 'signal' : ''}">
                    <div class="scan-symbol">${result.symbol}</div>
                    <div class="scan-details">
                        Signal: ${result.signal_type || 'None'}<br>
                        Confidence: ${result.confidence || 'N/A'}%<br>
                        Time: ${new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            `).join('');
        }

        function updateAnalysisPanel(analysis) {
            const analysisContent = document.getElementById('analysisContent');
            
            if (analysis.points && analysis.points.length > 0) {
                analysisContent.innerHTML = analysis.points.map((point, index) => `
                    <div class="analysis-point">
                        <div class="point-number">Point ${index + 1}</div>
                        <div class="point-content">${point}</div>
                    </div>
                `).join('');
            }
        }

        async function loadMarketData() {
            try {
                const response = await fetch(`${API_BASE}/market/overview`);
                if (response.ok) {
                    const data = await response.json();
                    updateMarketPanel(data.symbols || []);
                }
            } catch (error) {
                console.error('Market data error:', error);
            }

            // Refresh market data every 60 seconds
            setTimeout(loadMarketData, 60000);
        }

        function updateMarketPanel(symbols) {
            const marketContent = document.getElementById('marketData');
            
            if (symbols.length === 0) {
                marketContent.innerHTML = `
                    <div class="market-item">
                        <span class="market-symbol">Market data loading...</span>
                    </div>
                `;
                return;
            }

            marketContent.innerHTML = symbols.map(symbol => `
                <div class="market-item">
                    <span class="market-symbol">${symbol.symbol}</span>
                    <div>
                        <div class="market-price">$${symbol.price}</div>
                        <div class="market-change">${symbol.change > 0 ? '+' : ''}${symbol.change}%</div>
                    </div>
                </div>
            `).join('');
        }

        // Check connection every 30 seconds
        setInterval(checkServerConnection, 30000);
    </script>
</body>
</html>
