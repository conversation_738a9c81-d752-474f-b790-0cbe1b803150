#!/usr/bin/env python3
"""
A.T.L.A.S. Quick Installation Script
Automated installation and setup for the reorganized A.T.L.A.S. project
"""

import sys
import os
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Print installation banner"""
    print("=" * 70)
    print("🚀 A.T.L.A.S. QUICK INSTALLATION")
    print("   Advanced Trading & Learning Analysis System")
    print("   Automated Setup for Reorganized Project Structure")
    print("=" * 70)
    print()

def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible")
    return True

def install_python_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    try:
        # Check if requirements.txt exists
        req_file = Path("requirements.txt")
        if not req_file.exists():
            print("⚠️ requirements.txt not found, installing basic dependencies...")
            basic_deps = [
                "fastapi", "uvicorn", "pandas", "numpy", 
                "requests", "aiohttp", "python-multipart"
            ]
            
            for dep in basic_deps:
                print(f"Installing {dep}...")
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
        else:
            print("Installing from requirements.txt...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         check=True, capture_output=True)
        
        print("✅ Python dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Python dependencies: {e}")
        return False

def check_node_js():
    """Check if Node.js is available for desktop app"""
    print("\n🟢 Checking Node.js for desktop application...")
    
    try:
        result = subprocess.run(["node", "--version"], 
                              capture_output=True, text=True, check=True)
        version = result.stdout.strip()
        print(f"✅ Node.js {version} found")
        
        # Check npm
        npm_result = subprocess.run(["npm", "--version"], 
                                  capture_output=True, text=True, check=True)
        npm_version = npm_result.stdout.strip()
        print(f"✅ npm {npm_version} found")
        
        return True
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Node.js not found - desktop app will not be available")
        print("   Install Node.js from: https://nodejs.org/")
        return False

def install_desktop_app_dependencies():
    """Install desktop app dependencies"""
    print("\n🖥️ Installing desktop app dependencies...")
    
    desktop_path = Path("desktop_app")
    if not desktop_path.exists():
        print("❌ Desktop app directory not found")
        return False
    
    try:
        # Change to desktop app directory and install
        original_dir = os.getcwd()
        os.chdir(desktop_path)
        
        print("Running npm install...")
        subprocess.run(["npm", "install"], check=True, capture_output=True)
        
        os.chdir(original_dir)
        print("✅ Desktop app dependencies installed")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install desktop app dependencies: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating project directories...")
    
    directories = [
        "databases",
        "logs",
        "temp"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created: {directory}/")
        else:
            print(f"✅ Exists: {directory}/")
    
    return True

def run_setup_wizard():
    """Offer to run the setup wizard"""
    print("\n🧙 Configuration Setup")
    
    response = input("Would you like to run the interactive setup wizard? (Y/n): ").strip().lower()
    
    if response != 'n':
        try:
            print("Starting setup wizard...")
            subprocess.run([sys.executable, "atlas_setup_wizard.py"], check=True)
            print("✅ Setup wizard completed")
            return True
        except subprocess.CalledProcessError:
            print("⚠️ Setup wizard failed, you can run it later with: python atlas_setup_wizard.py")
            return False
    else:
        print("⏭️ Skipped setup wizard - you can run it later with: python atlas_setup_wizard.py")
        return True

def validate_installation():
    """Validate the installation"""
    print("\n🔍 Validating installation...")
    
    # Check critical files
    critical_files = [
        "minimal_atlas_startup.py",
        "atlas_setup_wizard.py",
        "atlas_comprehensive_startup.py",
        "4_helper_tools/atlas_demo_mode.py",
        "4_helper_tools/atlas_error_recovery.py",
        "4_helper_tools/atlas_robust_server_manager.py"
    ]
    
    missing_files = []
    for file_path in critical_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print("❌ Missing critical files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ All critical files present")
    return True

def create_platform_scripts():
    """Create platform-specific installation scripts"""
    print("\n📜 Creating platform-specific scripts...")

    # Windows batch script
    windows_script = """@echo off
echo ======================================================================
echo 🚀 A.T.L.A.S. AUTOMATED INSTALLATION - WINDOWS
echo ======================================================================
echo Advanced Trading ^& Learning Analysis System
echo ======================================================================

echo [STEP 1] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python 3.8+ from python.org
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo [STEP 2] Running A.T.L.A.S. installation...
python install_atlas.py

echo.
echo [STEP 3] Installation complete!
echo You can now run: python atlas_comprehensive_startup.py
pause
"""

    # Linux/Mac shell script
    unix_script = """#!/bin/bash
echo "======================================================================"
echo "🚀 A.T.L.A.S. AUTOMATED INSTALLATION - UNIX/LINUX/MAC"
echo "======================================================================"
echo "Advanced Trading & Learning Analysis System"
echo "======================================================================"

echo "[STEP 1] Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found. Please install Python 3.8+"
    exit 1
fi
echo "✅ Python3 found"

echo ""
echo "[STEP 2] Running A.T.L.A.S. installation..."
python3 install_atlas.py

echo ""
echo "[STEP 3] Installation complete!"
echo "You can now run: python3 atlas_comprehensive_startup.py"
"""

    try:
        # Write Windows script
        with open("install_atlas_windows.bat", "w", encoding="utf-8") as f:
            f.write(windows_script)
        print("✅ Created: install_atlas_windows.bat")

        # Write Unix script
        with open("install_atlas_unix.sh", "w", encoding="utf-8") as f:
            f.write(unix_script)

        # Make Unix script executable
        import stat
        os.chmod("install_atlas_unix.sh", stat.S_IRWXU | stat.S_IRGRP | stat.S_IROTH)
        print("✅ Created: install_atlas_unix.sh")

        return True
    except Exception as e:
        print(f"❌ Failed to create platform scripts: {e}")
        return False

def verify_system_requirements():
    """Verify comprehensive system requirements"""
    print("\n🔍 Verifying system requirements...")

    requirements = {
        'python_version': False,
        'pip_available': False,
        'git_available': False,
        'disk_space': False,
        'memory': False
    }

    # Check Python version
    if sys.version_info >= (3, 8):
        requirements['python_version'] = True
        print(f"✅ Python {sys.version.split()[0]} - Compatible")
    else:
        print(f"❌ Python {sys.version.split()[0]} - Requires 3.8+")

    # Check pip
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"],
                      capture_output=True, check=True)
        requirements['pip_available'] = True
        print("✅ pip - Available")
    except subprocess.CalledProcessError:
        print("❌ pip - Not available")

    # Check git (optional)
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        requirements['git_available'] = True
        print("✅ git - Available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ git - Not available (optional)")
        requirements['git_available'] = True  # Not required

    # Check disk space (at least 1GB)
    try:
        import shutil
        free_space = shutil.disk_usage('.').free
        if free_space > 1024**3:  # 1GB
            requirements['disk_space'] = True
            print(f"✅ Disk space - {free_space // (1024**3)}GB available")
        else:
            print(f"❌ Disk space - Only {free_space // (1024**2)}MB available, need 1GB+")
    except Exception:
        print("⚠️ Disk space - Could not check")
        requirements['disk_space'] = True  # Assume OK

    # Check memory (at least 2GB)
    try:
        import psutil
        memory = psutil.virtual_memory()
        if memory.total > 2 * 1024**3:  # 2GB
            requirements['memory'] = True
            print(f"✅ Memory - {memory.total // (1024**3)}GB available")
        else:
            print(f"⚠️ Memory - Only {memory.total // (1024**3)}GB available, recommend 2GB+")
            requirements['memory'] = True  # Not critical
    except ImportError:
        print("⚠️ Memory - Could not check (psutil not available)")
        requirements['memory'] = True  # Assume OK

    return all(requirements.values())

def show_next_steps():
    """Show next steps after installation"""
    print("\n" + "=" * 70)
    print("🎉 INSTALLATION COMPLETED!")
    print("=" * 70)

    print("\n📋 Next Steps:")
    print("1. 🧙 Run setup wizard (if not done):")
    print("   python atlas_setup_wizard.py")

    print("\n2. 🚀 Start A.T.L.A.S. system:")
    print("   python atlas_comprehensive_startup.py")

    print("\n3. 🖥️ Launch desktop application:")
    print("   cd desktop_app && npm start")

    print("\n4. 🎭 Test demo mode:")
    print("   python 4_helper_tools/atlas_demo_mode.py")

    print("\n5. 🏥 Monitor system health:")
    print("   python 4_helper_tools/atlas_health_monitor.py --dashboard")

    print("\n6. 📊 View health dashboard:")
    print("   Open browser to: http://localhost:9090")

    print("\n📚 Documentation:")
    print("   - README.md - Main project documentation")
    print("   - LEE_METHOD_README.md - Lee Method scanner guide")
    print("   - desktop_app/README.md - Desktop app instructions")

    print("\n🆘 If you encounter issues:")
    print("   - Try emergency startup: python atlas_emergency_startup.py")
    print("   - Check logs in: logs/ directory")
    print("   - Run validation: python 4_helper_tools/atlas_demo_mode.py")
    print("   - Enable debug logging: python 4_helper_tools/atlas_enhanced_logging.py")

    print("\n🔧 Advanced Features:")
    print("   - Error recovery: python 4_helper_tools/atlas_error_recovery.py")
    print("   - Graceful degradation: python 4_helper_tools/atlas_graceful_degradation.py")
    print("   - Robust server: python 4_helper_tools/atlas_robust_server_manager.py")

    print("\n" + "=" * 70)

def main():
    """Main installation function"""
    print_banner()

    # Verify system requirements
    if not verify_system_requirements():
        print("❌ System requirements not met")
        response = input("Continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            return 1

    # Check Python version
    if not check_python_version():
        return 1

    # Install Python dependencies
    if not install_python_dependencies():
        print("⚠️ Python dependency installation failed, but continuing...")

    # Check Node.js
    node_available = check_node_js()

    # Install desktop app dependencies if Node.js is available
    if node_available:
        if not install_desktop_app_dependencies():
            print("⚠️ Desktop app installation failed, but continuing...")

    # Create directories
    create_directories()

    # Create platform-specific scripts
    create_platform_scripts()

    # Validate installation
    if not validate_installation():
        print("❌ Installation validation failed")
        return 1

    # Offer setup wizard
    run_setup_wizard()

    # Show next steps
    show_next_steps()

    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)
