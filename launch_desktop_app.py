#!/usr/bin/env python3
"""
A.T.L.A.S<PERSON> Lee Method Scanner - Desktop App Launcher
Simple Python-based desktop application using webview
"""

import webview
import threading
import time
import requests
import sys
import os

class AtlasDesktopApp:
    def __init__(self):
        self.api_url = "http://localhost:5001"
        self.interface_file = "atlas_interface.html"
        
    def check_api_server(self):
        """Check if the Lee Method API server is running"""
        try:
            response = requests.get(f"{self.api_url}/api/lee-method/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def wait_for_api(self, max_wait=30):
        """Wait for API server to be available"""
        print("🔍 Checking for Lee Method API server...")
        
        for i in range(max_wait):
            if self.check_api_server():
                print("✅ Lee Method API server found!")
                return True
            
            if i == 0:
                print("⏳ Waiting for API server to start...")
            
            time.sleep(1)
        
        print("❌ API server not found. Please start the Lee Method API server first.")
        return False
    
    def create_window(self):
        """Create the desktop application window"""
        
        # Get the full path to the HTML file
        html_path = os.path.abspath(self.interface_file)
        
        if not os.path.exists(html_path):
            print(f"❌ Interface file not found: {html_path}")
            return False
        
        print(f"🚀 Starting A.T.L.A.S. Desktop Application...")
        print(f"📁 Loading interface: {html_path}")
        
        # Create the webview window
        webview.create_window(
            title="A.T.L.A.S. Lee Method Scanner",
            url=f"file:///{html_path}",
            width=1400,
            height=900,
            min_size=(1200, 800),
            resizable=True,
            fullscreen=False,
            minimized=False,
            on_top=False,
            shadow=True,
            focus=True
        )
        
        return True
    
    def run(self):
        """Run the desktop application"""
        print("=" * 70)
        print("🚀 A.T.L.A.S. LEE METHOD SCANNER - DESKTOP APPLICATION")
        print("=" * 70)
        print("Advanced Trading & Learning Analysis System")
        print("Professional Desktop Trading Scanner")
        print("=" * 70)
        
        # Check if API server is running
        if not self.wait_for_api():
            print("\n💡 To start the API server, run:")
            print("   python atlas_lee_method_api.py")
            print("\nThen run this desktop app again.")
            input("\nPress Enter to exit...")
            return False
        
        # Create and start the desktop window
        if not self.create_window():
            return False
        
        print("\n🎉 Desktop application started successfully!")
        print("🔗 Connected to Lee Method API server")
        print("📊 Real-time scanning active")
        print("\n💡 Features available:")
        print("   • Real-time Lee Method pattern detection")
        print("   • No CORS restrictions")
        print("   • Native desktop experience")
        print("   • Full API integration")
        
        # Start the webview
        try:
            webview.start(debug=False)
        except KeyboardInterrupt:
            print("\n👋 Desktop application closed by user")
        except Exception as e:
            print(f"\n❌ Error running desktop app: {e}")
            return False
        
        return True

def main():
    """Main entry point"""
    try:
        app = AtlasDesktopApp()
        success = app.run()
        
        if not success:
            input("\nPress Enter to exit...")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        input("\nPress Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
