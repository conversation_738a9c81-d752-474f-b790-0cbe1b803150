#!/usr/bin/env python3
"""
Test and Validation Script for Lee Method Implementation
Tests all components of the Lee Method scanner system
"""

import asyncio
import logging
import sys
import time
from datetime import datetime
import pandas as pd
import numpy as np

# Import Lee Method components
from lee_method_scanner import LeeMethodScanner, LeeMethodSignal
from atlas_lee_method_realtime_scanner import AtlasLeeMethodRealtimeScanner

# Configure logging with ASCII-safe format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

class LeeMethodTester:
    """Comprehensive tester for Lee Method implementation"""
    
    def __init__(self):
        self.logger = logger
        self.test_results = {}
        
    def log_test_result(self, test_name: str, passed: bool, details: str = ""):
        """Log test result"""
        status = "[PASS]" if passed else "[FAIL]"
        self.test_results[test_name] = passed
        
        print(f"{status} {test_name}")
        if details:
            print(f"      {details}")
        
        if not passed:
            self.logger.error(f"Test failed: {test_name} - {details}")
    
    async def test_basic_lee_method_scanner(self):
        """Test basic Lee Method scanner functionality"""
        print("\n[TEST] Testing Basic Lee Method Scanner")
        print("-" * 50)
        
        try:
            # Initialize scanner
            scanner = LeeMethodScanner()
            
            # Test 1: Scanner initialization
            self.log_test_result(
                "Scanner Initialization", 
                scanner is not None,
                "Scanner object created successfully"
            )
            
            # Test 2: Fetch historical data
            test_symbol = "AAPL"
            df = await scanner.fetch_historical_data(test_symbol, "1day", 100)
            
            self.log_test_result(
                "Historical Data Fetch",
                not df.empty and len(df) > 50,
                f"Retrieved {len(df)} bars for {test_symbol}"
            )
            
            # Test 3: Calculate momentum histogram
            if not df.empty:
                df_with_indicators = scanner.calculate_momentum_histogram(df)
                has_histogram = 'histogram' in df_with_indicators.columns
                has_momentum = 'momentum' in df_with_indicators.columns
                
                self.log_test_result(
                    "Momentum Histogram Calculation",
                    has_histogram and has_momentum,
                    f"Histogram: {has_histogram}, Momentum: {has_momentum}"
                )
                
                # Test 4: Pattern detection
                pattern_result = scanner.detect_lee_method_pattern(df_with_indicators)
                
                self.log_test_result(
                    "Lee Method Pattern Detection",
                    pattern_result is not None or True,  # Pattern may or may not be found
                    f"Pattern detection completed: {pattern_result is not None}"
                )
                
                # Test 5: Signal generation
                signal = await scanner.generate_lee_method_signal(test_symbol, df)
                
                self.log_test_result(
                    "Lee Method Signal Generation",
                    signal is None or isinstance(signal, LeeMethodSignal),
                    f"Signal generated: {signal is not None}"
                )
                
                if signal:
                    print(f"      [SIGNAL] {signal.symbol}: {signal.signal_type}")
                    print(f"      [CONFIDENCE] {signal.confidence:.1%}")
                    print(f"      [ENTRY] ${signal.entry_price:.2f}")
                    print(f"      [TARGET] ${signal.target_price:.2f}")
                    print(f"      [STOP] ${signal.stop_loss:.2f}")
            
        except Exception as e:
            self.log_test_result(
                "Basic Scanner Test",
                False,
                f"Exception: {str(e)}"
            )
    
    async def test_lee_method_criteria(self):
        """Test Lee Method criteria implementation"""
        print("\n[TEST] Testing Lee Method Criteria")
        print("-" * 50)
        
        try:
            scanner = LeeMethodScanner()
            
            # Create test data that should trigger Lee Method pattern
            test_data = self.create_test_pattern_data()
            
            # Test Criterion 1: Decreasing bars followed by increase
            histogram_values = test_data['histogram'].values
            pattern_result = scanner._find_decreasing_increasing_pattern(histogram_values)
            
            self.log_test_result(
                "Criterion 1: Decreasing-Increasing Pattern",
                pattern_result is not None,
                f"Pattern found: {pattern_result is not None}"
            )
            
            if pattern_result:
                # Test Criterion 2: Momentum confirmation
                momentum_values = test_data['momentum'].values
                momentum_confirmed = scanner._check_momentum_confirmation(
                    momentum_values, pattern_result['increase_index']
                )
                
                self.log_test_result(
                    "Criterion 2: Momentum Confirmation",
                    momentum_confirmed,
                    f"Momentum confirmed: {momentum_confirmed}"
                )
                
                # Test Criterion 3: Multi-timeframe analysis
                trend_analysis = scanner._analyze_multi_timeframe_trends(test_data)
                
                self.log_test_result(
                    "Criterion 3: Multi-timeframe Analysis",
                    'daily_trend' in trend_analysis and 'weekly_trend' in trend_analysis,
                    f"Daily: {trend_analysis.get('daily_trend')}, Weekly: {trend_analysis.get('weekly_trend')}"
                )
        
        except Exception as e:
            self.log_test_result(
                "Lee Method Criteria Test",
                False,
                f"Exception: {str(e)}"
            )
    
    def create_test_pattern_data(self) -> pd.DataFrame:
        """Create test data with Lee Method pattern"""
        # Create 50 bars of test data
        dates = pd.date_range(start='2024-01-01', periods=50, freq='D')
        
        # Base price data
        np.random.seed(42)  # For reproducible results
        prices = 100 + np.cumsum(np.random.randn(50) * 0.5)
        
        df = pd.DataFrame({
            'date': dates,
            'open': prices,
            'high': prices + np.random.rand(50) * 2,
            'low': prices - np.random.rand(50) * 2,
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, 50)
        })
        
        # Create Lee Method pattern in histogram
        # Last 6 bars: 3 decreasing, then 1 increasing
        histogram = np.random.randn(50) * 0.1
        histogram[-6] = 0.5   # Start high
        histogram[-5] = 0.3   # Decrease
        histogram[-4] = 0.1   # Decrease
        histogram[-3] = -0.1  # Decrease
        histogram[-2] = 0.2   # Increase (Lee Method trigger)
        histogram[-1] = 0.3   # Continue increase
        
        # Create momentum that confirms the pattern
        momentum = np.random.randn(50) * 0.1
        momentum[-2] = 0.15   # Higher than previous
        momentum[-1] = 0.20   # Even higher
        
        df['histogram'] = histogram
        df['momentum'] = momentum
        
        # Add EMAs for trend analysis
        df['ema_5'] = df['close'].ewm(span=5).mean()
        df['ema_8'] = df['close'].ewm(span=8).mean()
        df['ema_21'] = df['close'].ewm(span=21).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()
        
        return df
    
    async def test_realtime_scanner(self):
        """Test real-time scanner functionality"""
        print("\n[TEST] Testing Real-time Scanner")
        print("-" * 50)
        
        try:
            # Initialize real-time scanner
            rt_scanner = AtlasLeeMethodRealtimeScanner()
            
            self.log_test_result(
                "Real-time Scanner Initialization",
                rt_scanner is not None,
                "Real-time scanner created successfully"
            )
            
            # Test single scan
            await rt_scanner._perform_lee_method_scan()
            
            # Get results
            signals = rt_scanner.get_latest_signals(5)
            status = rt_scanner.get_scanner_status()
            
            self.log_test_result(
                "Real-time Scanner Execution",
                status is not None,
                f"Scan completed, found {len(signals)} signals"
            )
            
            self.log_test_result(
                "Scanner Status Retrieval",
                'scan_count' in status,
                f"Status: {status.get('scan_count', 0)} scans completed"
            )
            
            # Test signal retrieval
            if signals:
                print(f"      [SIGNALS] Found {len(signals)} signals:")
                for signal in signals[:3]:  # Show first 3
                    print(f"         {signal['symbol']}: {signal['signal_type']} ({signal['confidence']:.1%})")
            
        except Exception as e:
            self.log_test_result(
                "Real-time Scanner Test",
                False,
                f"Exception: {str(e)}"
            )
    
    async def run_all_tests(self):
        """Run all Lee Method tests"""
        print("=" * 60)
        print("A.T.L.A.S. LEE METHOD IMPLEMENTATION TEST SUITE")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run all tests
        await self.test_basic_lee_method_scanner()
        await self.test_lee_method_criteria()
        await self.test_realtime_scanner()
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for test_name, passed in self.test_results.items():
                if not passed:
                    print(f"  - {test_name}")
        
        print("\n[INFO] Lee Method implementation testing completed!")
        
        return failed_tests == 0

async def main():
    """Main test function"""
    tester = LeeMethodTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n[SUCCESS] All tests passed! Lee Method implementation is ready.")
        sys.exit(0)
    else:
        print("\n[WARNING] Some tests failed. Please review the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
