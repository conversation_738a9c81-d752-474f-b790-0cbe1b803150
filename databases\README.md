# A.T.L.A.S. Databases

This folder contains all A.T.L.A.S. database files for organized data management.

## Database Files:
- `atlas.db` - Main A.T.L.A.S. database
- `atlas_memory.db` - Conversation memory and context
- `atlas_rag.db` - RAG (Retrieval-Augmented Generation) database
- `atlas_compliance.db` - Compliance and regulatory data
- `atlas_feedback.db` - User feedback and system learning
- `atlas_enhanced_memory.db` - Enhanced memory features
- `atlas_auto_trading.db` - Automated trading data

## Usage:
These databases are automatically accessed by the A.T.L.A.S. system components. No manual intervention required.
