#!/usr/bin/env python3
"""
A.T.L.A.S. Enhanced Logging and Debugging System
Structured ASCII-safe logging across all 42+ A.T.L.A.S. components with debug mode
"""

import sys
import os
import logging
import logging.handlers
import json
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading
from dataclasses import dataclass, asdict

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.extend([
    str(project_root),
    str(project_root / "4_helper_tools")
])

@dataclass
class LogEntry:
    """Structured log entry"""
    timestamp: str
    level: str
    component: str
    message: str
    details: Dict[str, Any]
    trace_id: Optional[str] = None
    user_id: Optional[str] = None

class ASCIISafeFormatter(logging.Formatter):
    """ASCII-safe formatter to prevent Unicode errors"""
    
    def format(self, record):
        # Convert any Unicode characters to ASCII-safe equivalents
        try:
            msg = super().format(record)
            # Ensure message is ASCII-safe
            return msg.encode('ascii', errors='replace').decode('ascii')
        except Exception:
            # Fallback to basic formatting
            return f"{record.levelname}: {str(record.getMessage()).encode('ascii', errors='replace').decode('ascii')}"

class AtlasLogger:
    """Enhanced logging system for A.T.L.A.S."""
    
    def __init__(self):
        self.project_root = project_root
        self.logs_dir = project_root / "logs"
        self.logs_dir.mkdir(exist_ok=True)
        
        self.debug_mode = False
        self.log_aggregation = []
        self.max_aggregation_size = 1000
        self.component_loggers = {}
        self.performance_metrics = {}
        
        # Setup main logger
        self.setup_main_logger()
        
        # Component-specific log files
        self.component_log_files = {
            'atlas_server': 'atlas_server.log',
            'lee_method_scanner': 'lee_method_scanner.log',
            'trading_engine': 'trading_engine.log',
            'market_data': 'market_data.log',
            'ai_engine': 'ai_engine.log',
            'database': 'database.log',
            'desktop_app': 'desktop_app.log',
            'error_recovery': 'error_recovery.log',
            'health_monitor': 'health_monitor.log',
            'demo_mode': 'demo_mode.log'
        }
        
        # Setup component loggers
        self.setup_component_loggers()
    
    def setup_main_logger(self):
        """Setup main A.T.L.A.S. logger"""
        # Create main logger
        self.main_logger = logging.getLogger('atlas')
        self.main_logger.setLevel(logging.DEBUG)
        
        # Clear existing handlers
        self.main_logger.handlers.clear()
        
        # ASCII-safe formatter
        formatter = ASCIISafeFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.main_logger.addHandler(console_handler)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            self.logs_dir / 'atlas_main.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8',
            errors='replace'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.main_logger.addHandler(file_handler)
        
        # Error file handler
        error_handler = logging.handlers.RotatingFileHandler(
            self.logs_dir / 'atlas_errors.log',
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8',
            errors='replace'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.main_logger.addHandler(error_handler)
    
    def setup_component_loggers(self):
        """Setup individual component loggers"""
        for component, log_file in self.component_log_files.items():
            logger = logging.getLogger(f'atlas.{component}')
            logger.setLevel(logging.DEBUG)
            
            # Clear existing handlers
            logger.handlers.clear()
            
            # ASCII-safe formatter
            formatter = ASCIISafeFormatter(
                f'%(asctime)s - {component.upper()} - %(levelname)s - %(message)s'
            )
            
            # Component-specific file handler
            file_handler = logging.handlers.RotatingFileHandler(
                self.logs_dir / log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8',
                errors='replace'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
            # Don't propagate to parent logger to avoid duplication
            logger.propagate = False
            
            self.component_loggers[component] = logger
    
    def get_logger(self, component: str) -> logging.Logger:
        """Get logger for a specific component"""
        if component in self.component_loggers:
            return self.component_loggers[component]
        else:
            # Create new component logger on demand
            logger = logging.getLogger(f'atlas.{component}')
            logger.setLevel(logging.DEBUG)
            
            formatter = ASCIISafeFormatter(
                f'%(asctime)s - {component.upper()} - %(levelname)s - %(message)s'
            )
            
            file_handler = logging.handlers.RotatingFileHandler(
                self.logs_dir / f'{component}.log',
                maxBytes=5*1024*1024,
                backupCount=3,
                encoding='utf-8',
                errors='replace'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            logger.propagate = False
            
            self.component_loggers[component] = logger
            return logger
    
    def enable_debug_mode(self):
        """Enable debug mode with detailed API call logging"""
        self.debug_mode = True
        
        # Set all loggers to DEBUG level
        for logger in self.component_loggers.values():
            logger.setLevel(logging.DEBUG)
        
        self.main_logger.info("🔍 Debug mode enabled - detailed logging active")
    
    def disable_debug_mode(self):
        """Disable debug mode"""
        self.debug_mode = False
        
        # Set loggers back to INFO level
        for logger in self.component_loggers.values():
            logger.setLevel(logging.INFO)
        
        self.main_logger.info("🔍 Debug mode disabled")
    
    def log_api_call(self, component: str, endpoint: str, method: str = "GET", 
                     params: Dict[str, Any] = None, response_time: float = None,
                     status_code: int = None, error: str = None):
        """Log API call details in debug mode"""
        if not self.debug_mode:
            return
        
        logger = self.get_logger(component)
        
        api_details = {
            'endpoint': endpoint,
            'method': method,
            'params': params or {},
            'response_time': response_time,
            'status_code': status_code,
            'error': error
        }
        
        # Convert to ASCII-safe JSON
        try:
            details_str = json.dumps(api_details, ensure_ascii=True)
        except Exception:
            details_str = str(api_details).encode('ascii', errors='replace').decode('ascii')
        
        if error:
            logger.error(f"API_CALL_FAILED: {details_str}")
        elif status_code and status_code >= 400:
            logger.warning(f"API_CALL_ERROR: {details_str}")
        else:
            logger.debug(f"API_CALL: {details_str}")
    
    def log_performance_metric(self, component: str, operation: str, 
                              duration: float, details: Dict[str, Any] = None):
        """Log performance metrics"""
        logger = self.get_logger(component)
        
        # Store in performance metrics
        if component not in self.performance_metrics:
            self.performance_metrics[component] = []
        
        metric = {
            'timestamp': time.time(),
            'operation': operation,
            'duration': duration,
            'details': details or {}
        }
        
        self.performance_metrics[component].append(metric)
        
        # Keep only recent metrics
        if len(self.performance_metrics[component]) > 100:
            self.performance_metrics[component] = self.performance_metrics[component][-100:]
        
        # Log performance
        if duration > 5.0:  # Slow operation
            logger.warning(f"SLOW_OPERATION: {operation} took {duration:.3f}s")
        elif self.debug_mode:
            logger.debug(f"PERFORMANCE: {operation} completed in {duration:.3f}s")
    
    def log_structured(self, component: str, level: str, message: str, 
                      details: Dict[str, Any] = None, trace_id: str = None,
                      user_id: str = None):
        """Log structured data"""
        logger = self.get_logger(component)
        
        # Create structured log entry
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level.upper(),
            component=component,
            message=message,
            details=details or {},
            trace_id=trace_id,
            user_id=user_id
        )
        
        # Add to aggregation
        self.log_aggregation.append(entry)
        if len(self.log_aggregation) > self.max_aggregation_size:
            self.log_aggregation.pop(0)
        
        # Convert to ASCII-safe string
        try:
            entry_str = json.dumps(asdict(entry), ensure_ascii=True)
        except Exception:
            entry_str = f"{level}: {message}".encode('ascii', errors='replace').decode('ascii')
        
        # Log at appropriate level
        log_level = getattr(logging, level.upper(), logging.INFO)
        logger.log(log_level, f"STRUCTURED: {entry_str}")
    
    def log_error_with_traceback(self, component: str, error: Exception, 
                                context: Dict[str, Any] = None):
        """Log error with full traceback"""
        logger = self.get_logger(component)
        
        error_details = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {}
        }
        
        # ASCII-safe error logging
        try:
            error_str = json.dumps(error_details, ensure_ascii=True)
        except Exception:
            error_str = f"Error: {str(error)}".encode('ascii', errors='replace').decode('ascii')
        
        logger.error(f"EXCEPTION: {error_str}")
    
    def get_log_aggregation(self, component: str = None, level: str = None,
                           limit: int = 100) -> List[Dict[str, Any]]:
        """Get aggregated logs with filtering"""
        logs = self.log_aggregation
        
        # Filter by component
        if component:
            logs = [log for log in logs if log.component == component]
        
        # Filter by level
        if level:
            logs = [log for log in logs if log.level == level.upper()]
        
        # Limit results
        logs = logs[-limit:] if limit else logs
        
        return [asdict(log) for log in logs]
    
    def get_performance_report(self, component: str = None) -> Dict[str, Any]:
        """Get performance metrics report"""
        if component:
            metrics = self.performance_metrics.get(component, [])
            components = [component]
        else:
            metrics = []
            for comp_metrics in self.performance_metrics.values():
                metrics.extend(comp_metrics)
            components = list(self.performance_metrics.keys())
        
        if not metrics:
            return {'error': 'No performance data available'}
        
        # Calculate statistics
        durations = [m['duration'] for m in metrics]
        
        report = {
            'components': components,
            'total_operations': len(metrics),
            'avg_duration': sum(durations) / len(durations),
            'min_duration': min(durations),
            'max_duration': max(durations),
            'slow_operations': len([d for d in durations if d > 5.0]),
            'recent_metrics': metrics[-10:]  # Last 10 operations
        }
        
        return report
    
    def export_logs(self, output_file: str, component: str = None, 
                   start_time: str = None, end_time: str = None):
        """Export logs to file"""
        logs = self.get_log_aggregation(component=component)
        
        # Filter by time if specified
        if start_time or end_time:
            filtered_logs = []
            for log in logs:
                log_time = log['timestamp']
                if start_time and log_time < start_time:
                    continue
                if end_time and log_time > end_time:
                    continue
                filtered_logs.append(log)
            logs = filtered_logs
        
        # Export to file
        try:
            with open(output_file, 'w', encoding='utf-8', errors='replace') as f:
                json.dump(logs, f, indent=2, ensure_ascii=True)
            
            self.main_logger.info(f"Logs exported to {output_file}")
            return True
        except Exception as e:
            self.main_logger.error(f"Failed to export logs: {e}")
            return False
    
    def cleanup_old_logs(self, days_to_keep: int = 7):
        """Clean up old log files"""
        import time
        
        cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
        cleaned_files = []
        
        for log_file in self.logs_dir.glob("*.log*"):
            try:
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    cleaned_files.append(str(log_file))
            except Exception as e:
                self.main_logger.warning(f"Failed to clean up {log_file}: {e}")
        
        if cleaned_files:
            self.main_logger.info(f"Cleaned up {len(cleaned_files)} old log files")
        
        return cleaned_files

# Global logger instance
atlas_logger = AtlasLogger()

def get_atlas_logger() -> AtlasLogger:
    """Get global A.T.L.A.S. logger instance"""
    return atlas_logger

def get_component_logger(component: str) -> logging.Logger:
    """Get logger for a specific component"""
    return atlas_logger.get_logger(component)

def log_api_call(component: str, endpoint: str, **kwargs):
    """Log API call (convenience function)"""
    atlas_logger.log_api_call(component, endpoint, **kwargs)

def log_performance(component: str, operation: str, duration: float, **kwargs):
    """Log performance metric (convenience function)"""
    atlas_logger.log_performance_metric(component, operation, duration, **kwargs)

def log_error(component: str, error: Exception, context: Dict[str, Any] = None):
    """Log error with traceback (convenience function)"""
    atlas_logger.log_error_with_traceback(component, error, context)

def enable_debug_logging():
    """Enable debug mode (convenience function)"""
    atlas_logger.enable_debug_mode()

def disable_debug_logging():
    """Disable debug mode (convenience function)"""
    atlas_logger.disable_debug_mode()

if __name__ == "__main__":
    # Test logging system
    print("📝 A.T.L.A.S. Enhanced Logging System")
    print("=" * 50)
    
    # Test component logging
    test_logger = get_component_logger('test_component')
    test_logger.info("Testing component logging")
    
    # Test API call logging
    enable_debug_logging()
    log_api_call('test_component', '/api/test', method='POST', response_time=0.123)
    
    # Test performance logging
    log_performance('test_component', 'test_operation', 2.5)
    
    # Test error logging
    try:
        raise ValueError("Test error")
    except Exception as e:
        log_error('test_component', e, {'context': 'testing'})
    
    # Get performance report
    report = atlas_logger.get_performance_report()
    print(f"\nPerformance Report: {json.dumps(report, indent=2)}")
    
    print("\n✅ Logging system test completed")
