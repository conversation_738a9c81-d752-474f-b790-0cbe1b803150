#!/usr/bin/env python3
"""
A.T.L.A.S. Graceful Component Degradation System
Enable components to work with reduced features when dependencies fail
"""

import sys
import os
import logging
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Set
from enum import Enum
from dataclasses import dataclass
import json

# Add project paths
project_root = Path(__file__).parent.parent
sys.path.extend([
    str(project_root),
    str(project_root / "4_helper_tools")
])

logger = logging.getLogger(__name__)

class ComponentStatus(Enum):
    """Component status levels"""
    FULL = "full"
    DEGRADED = "degraded"
    MINIMAL = "minimal"
    OFFLINE = "offline"

class FeatureLevel(Enum):
    """Feature availability levels"""
    ESSENTIAL = "essential"
    STANDARD = "standard"
    ADVANCED = "advanced"
    PREMIUM = "premium"

@dataclass
class ComponentConfig:
    """Configuration for component degradation"""
    name: str
    dependencies: List[str]
    features: Dict[FeatureLevel, List[str]]
    fallback_modes: Dict[ComponentStatus, Dict[str, Any]]
    health_check: Optional[Callable] = None

@dataclass
class DegradationState:
    """Current degradation state of a component"""
    component: str
    status: ComponentStatus
    available_features: List[str]
    disabled_features: List[str]
    fallback_data: Dict[str, Any]
    last_update: float
    reason: str

class AtlasGracefulDegradation:
    """Graceful degradation manager for A.T.L.A.S. components"""
    
    def __init__(self):
        self.components = {}
        self.degradation_states = {}
        self.feature_registry = {}
        self.dependency_graph = {}
        self.monitoring_interval = 30
        self.setup_component_configs()
        
    def setup_component_configs(self):
        """Setup configuration for all A.T.L.A.S. components"""
        
        # Main Chat Engine
        self.register_component(ComponentConfig(
            name="atlas_chat_engine",
            dependencies=["openai_api", "database"],
            features={
                FeatureLevel.ESSENTIAL: ["basic_chat", "text_responses"],
                FeatureLevel.STANDARD: ["ai_responses", "conversation_memory"],
                FeatureLevel.ADVANCED: ["complex_analysis", "multi_turn_context"],
                FeatureLevel.PREMIUM: ["real_time_learning", "personalization"]
            },
            fallback_modes={
                ComponentStatus.FULL: {"mode": "ai_powered", "data_source": "openai"},
                ComponentStatus.DEGRADED: {"mode": "template_based", "data_source": "local_templates"},
                ComponentStatus.MINIMAL: {"mode": "static_responses", "data_source": "hardcoded"},
                ComponentStatus.OFFLINE: {"mode": "error_message", "data_source": "none"}
            }
        ))
        
        # Lee Method Scanner
        self.register_component(ComponentConfig(
            name="lee_method_scanner",
            dependencies=["market_data_api", "database"],
            features={
                FeatureLevel.ESSENTIAL: ["pattern_detection", "basic_signals"],
                FeatureLevel.STANDARD: ["real_time_scanning", "confidence_scoring"],
                FeatureLevel.ADVANCED: ["multi_timeframe_analysis", "trend_confirmation"],
                FeatureLevel.PREMIUM: ["predictive_analytics", "risk_assessment"]
            },
            fallback_modes={
                ComponentStatus.FULL: {"mode": "real_time", "data_source": "live_api"},
                ComponentStatus.DEGRADED: {"mode": "cached_data", "data_source": "local_cache"},
                ComponentStatus.MINIMAL: {"mode": "demo_patterns", "data_source": "mock_data"},
                ComponentStatus.OFFLINE: {"mode": "historical_only", "data_source": "stored_data"}
            }
        ))
        
        # Trading Engine
        self.register_component(ComponentConfig(
            name="atlas_trading_engine",
            dependencies=["market_data_api", "trading_api", "risk_engine"],
            features={
                FeatureLevel.ESSENTIAL: ["position_tracking", "basic_orders"],
                FeatureLevel.STANDARD: ["automated_trading", "risk_management"],
                FeatureLevel.ADVANCED: ["portfolio_optimization", "advanced_strategies"],
                FeatureLevel.PREMIUM: ["ai_trading", "dynamic_hedging"]
            },
            fallback_modes={
                ComponentStatus.FULL: {"mode": "live_trading", "safety": "full_risk_management"},
                ComponentStatus.DEGRADED: {"mode": "paper_trading", "safety": "simulation_only"},
                ComponentStatus.MINIMAL: {"mode": "analysis_only", "safety": "no_execution"},
                ComponentStatus.OFFLINE: {"mode": "disabled", "safety": "all_blocked"}
            }
        ))
        
        # Market Data Engine
        self.register_component(ComponentConfig(
            name="atlas_market_engine",
            dependencies=["external_apis", "database"],
            features={
                FeatureLevel.ESSENTIAL: ["basic_quotes", "price_data"],
                FeatureLevel.STANDARD: ["real_time_data", "volume_analysis"],
                FeatureLevel.ADVANCED: ["options_data", "sentiment_analysis"],
                FeatureLevel.PREMIUM: ["institutional_data", "alternative_data"]
            },
            fallback_modes={
                ComponentStatus.FULL: {"mode": "real_time", "refresh": "live"},
                ComponentStatus.DEGRADED: {"mode": "delayed_data", "refresh": "15min"},
                ComponentStatus.MINIMAL: {"mode": "daily_data", "refresh": "eod"},
                ComponentStatus.OFFLINE: {"mode": "cached_only", "refresh": "none"}
            }
        ))
        
        # Desktop Application
        self.register_component(ComponentConfig(
            name="atlas_desktop_app",
            dependencies=["atlas_server", "electron"],
            features={
                FeatureLevel.ESSENTIAL: ["basic_interface", "connection_status"],
                FeatureLevel.STANDARD: ["full_interface", "real_time_updates"],
                FeatureLevel.ADVANCED: ["desktop_notifications", "keyboard_shortcuts"],
                FeatureLevel.PREMIUM: ["multi_monitor", "custom_layouts"]
            },
            fallback_modes={
                ComponentStatus.FULL: {"interface": "desktop_native", "updates": "real_time"},
                ComponentStatus.DEGRADED: {"interface": "web_wrapper", "updates": "polling"},
                ComponentStatus.MINIMAL: {"interface": "basic_web", "updates": "manual"},
                ComponentStatus.OFFLINE: {"interface": "error_page", "updates": "none"}
            }
        ))
    
    def register_component(self, config: ComponentConfig):
        """Register a component configuration"""
        self.components[config.name] = config
        
        # Initialize degradation state
        self.degradation_states[config.name] = DegradationState(
            component=config.name,
            status=ComponentStatus.FULL,
            available_features=self.get_features_for_level(config, FeatureLevel.PREMIUM),
            disabled_features=[],
            fallback_data=config.fallback_modes[ComponentStatus.FULL],
            last_update=time.time(),
            reason="initialized"
        )
        
        # Build dependency graph
        self.dependency_graph[config.name] = config.dependencies
        
        # Register features
        for level, features in config.features.items():
            for feature in features:
                if feature not in self.feature_registry:
                    self.feature_registry[feature] = []
                self.feature_registry[feature].append(config.name)
    
    def get_features_for_level(self, config: ComponentConfig, max_level: FeatureLevel) -> List[str]:
        """Get all features available up to a certain level"""
        features = []
        level_order = [FeatureLevel.ESSENTIAL, FeatureLevel.STANDARD, FeatureLevel.ADVANCED, FeatureLevel.PREMIUM]
        
        for level in level_order:
            features.extend(config.features.get(level, []))
            if level == max_level:
                break
        
        return features
    
    def check_dependency_health(self, component_name: str) -> Dict[str, bool]:
        """Check health of component dependencies"""
        if component_name not in self.components:
            return {}
        
        dependencies = self.dependency_graph[component_name]
        health_status = {}
        
        for dependency in dependencies:
            try:
                # Check different types of dependencies
                if dependency == "openai_api":
                    health_status[dependency] = self.check_openai_health()
                elif dependency == "database":
                    health_status[dependency] = self.check_database_health()
                elif dependency == "market_data_api":
                    health_status[dependency] = self.check_market_data_health()
                elif dependency == "trading_api":
                    health_status[dependency] = self.check_trading_api_health()
                elif dependency == "atlas_server":
                    health_status[dependency] = self.check_server_health()
                else:
                    health_status[dependency] = True  # Assume healthy if unknown
                    
            except Exception as e:
                logger.error(f"Error checking dependency {dependency}: {e}")
                health_status[dependency] = False
        
        return health_status
    
    def check_openai_health(self) -> bool:
        """Check OpenAI API health"""
        try:
            # Try to get API key from config
            from config import OPENAI_API_KEY
            return bool(OPENAI_API_KEY and OPENAI_API_KEY != "your_openai_api_key_here")
        except ImportError:
            return False
    
    def check_database_health(self) -> bool:
        """Check database health"""
        try:
            db_path = project_root / "databases"
            return db_path.exists() and any(db_path.glob("*.db"))
        except Exception:
            return False
    
    def check_market_data_health(self) -> bool:
        """Check market data API health"""
        try:
            # Check if any market data APIs are configured
            from config import ALPHA_VANTAGE_API_KEY, INCITE_API_KEY
            return any([
                ALPHA_VANTAGE_API_KEY and ALPHA_VANTAGE_API_KEY != "your_alpha_vantage_api_key_here",
                INCITE_API_KEY and INCITE_API_KEY != "your_incite_api_key_here"
            ])
        except ImportError:
            return False
    
    def check_trading_api_health(self) -> bool:
        """Check trading API health"""
        # For now, assume trading is in paper mode
        return True
    
    def check_server_health(self) -> bool:
        """Check A.T.L.A.S. server health"""
        try:
            import requests
            response = requests.get("http://localhost:8080/health", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
    
    def determine_component_status(self, component_name: str) -> ComponentStatus:
        """Determine appropriate status level for a component"""
        dependency_health = self.check_dependency_health(component_name)
        
        if not dependency_health:
            return ComponentStatus.OFFLINE
        
        healthy_deps = sum(dependency_health.values())
        total_deps = len(dependency_health)
        
        if total_deps == 0:
            return ComponentStatus.FULL
        
        health_ratio = healthy_deps / total_deps
        
        if health_ratio >= 1.0:
            return ComponentStatus.FULL
        elif health_ratio >= 0.7:
            return ComponentStatus.DEGRADED
        elif health_ratio >= 0.3:
            return ComponentStatus.MINIMAL
        else:
            return ComponentStatus.OFFLINE
    
    def apply_degradation(self, component_name: str, new_status: ComponentStatus, reason: str = "dependency_failure"):
        """Apply degradation to a component"""
        if component_name not in self.components:
            logger.error(f"Unknown component: {component_name}")
            return
        
        config = self.components[component_name]
        current_state = self.degradation_states[component_name]
        
        # Determine available features based on status
        if new_status == ComponentStatus.FULL:
            max_level = FeatureLevel.PREMIUM
        elif new_status == ComponentStatus.DEGRADED:
            max_level = FeatureLevel.ADVANCED
        elif new_status == ComponentStatus.MINIMAL:
            max_level = FeatureLevel.ESSENTIAL
        else:  # OFFLINE
            max_level = None
        
        if max_level:
            available_features = self.get_features_for_level(config, max_level)
            all_features = self.get_features_for_level(config, FeatureLevel.PREMIUM)
            disabled_features = [f for f in all_features if f not in available_features]
        else:
            available_features = []
            disabled_features = self.get_features_for_level(config, FeatureLevel.PREMIUM)
        
        # Update degradation state
        self.degradation_states[component_name] = DegradationState(
            component=component_name,
            status=new_status,
            available_features=available_features,
            disabled_features=disabled_features,
            fallback_data=config.fallback_modes[new_status],
            last_update=time.time(),
            reason=reason
        )
        
        logger.info(f"Component {component_name} degraded to {new_status.value}: {reason}")
        
        # Trigger cascading degradation for dependent components
        self.check_cascading_effects(component_name)
    
    def check_cascading_effects(self, failed_component: str):
        """Check for cascading effects when a component fails"""
        for component_name, dependencies in self.dependency_graph.items():
            if failed_component in dependencies:
                current_status = self.degradation_states[component_name].status
                new_status = self.determine_component_status(component_name)
                
                if new_status != current_status:
                    self.apply_degradation(
                        component_name, 
                        new_status, 
                        f"dependency_failure: {failed_component}"
                    )
    
    def get_component_capabilities(self, component_name: str) -> Dict[str, Any]:
        """Get current capabilities of a component"""
        if component_name not in self.degradation_states:
            return {"error": "Component not found"}
        
        state = self.degradation_states[component_name]
        
        return {
            "component": component_name,
            "status": state.status.value,
            "available_features": state.available_features,
            "disabled_features": state.disabled_features,
            "fallback_mode": state.fallback_data,
            "last_update": state.last_update,
            "reason": state.reason
        }
    
    def is_feature_available(self, feature_name: str, component_name: str = None) -> bool:
        """Check if a specific feature is available"""
        if component_name:
            # Check specific component
            if component_name in self.degradation_states:
                return feature_name in self.degradation_states[component_name].available_features
            return False
        else:
            # Check any component that provides this feature
            if feature_name in self.feature_registry:
                for comp in self.feature_registry[feature_name]:
                    if comp in self.degradation_states:
                        if feature_name in self.degradation_states[comp].available_features:
                            return True
            return False
    
    def get_system_degradation_report(self) -> Dict[str, Any]:
        """Get comprehensive degradation report for the entire system"""
        report = {
            "timestamp": time.time(),
            "overall_status": "unknown",
            "components": {},
            "degraded_components": [],
            "offline_components": [],
            "available_features": [],
            "disabled_features": []
        }
        
        all_available_features = set()
        all_disabled_features = set()
        status_counts = {status: 0 for status in ComponentStatus}
        
        for component_name, state in self.degradation_states.items():
            report["components"][component_name] = {
                "status": state.status.value,
                "available_features": len(state.available_features),
                "disabled_features": len(state.disabled_features),
                "reason": state.reason
            }
            
            status_counts[state.status] += 1
            all_available_features.update(state.available_features)
            all_disabled_features.update(state.disabled_features)
            
            if state.status == ComponentStatus.DEGRADED:
                report["degraded_components"].append(component_name)
            elif state.status == ComponentStatus.OFFLINE:
                report["offline_components"].append(component_name)
        
        report["available_features"] = list(all_available_features)
        report["disabled_features"] = list(all_disabled_features)
        
        # Determine overall system status
        total_components = len(self.degradation_states)
        if status_counts[ComponentStatus.OFFLINE] > total_components / 2:
            report["overall_status"] = "critical"
        elif status_counts[ComponentStatus.DEGRADED] > total_components / 3:
            report["overall_status"] = "degraded"
        elif status_counts[ComponentStatus.FULL] == total_components:
            report["overall_status"] = "optimal"
        else:
            report["overall_status"] = "functional"
        
        return report
    
    async def monitor_and_adjust(self):
        """Continuously monitor and adjust component degradation"""
        logger.info("Starting graceful degradation monitoring...")
        
        while True:
            try:
                for component_name in self.components.keys():
                    current_status = self.degradation_states[component_name].status
                    optimal_status = self.determine_component_status(component_name)
                    
                    if optimal_status != current_status:
                        self.apply_degradation(component_name, optimal_status, "health_check_update")
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in degradation monitoring: {e}")
                await asyncio.sleep(self.monitoring_interval)

# Global degradation manager
degradation_manager = AtlasGracefulDegradation()

def get_degradation_manager() -> AtlasGracefulDegradation:
    """Get global degradation manager instance"""
    return degradation_manager

def is_feature_available(feature_name: str, component_name: str = None) -> bool:
    """Check if a feature is available (convenience function)"""
    return degradation_manager.is_feature_available(feature_name, component_name)

def get_component_status(component_name: str) -> str:
    """Get component status (convenience function)"""
    if component_name in degradation_manager.degradation_states:
        return degradation_manager.degradation_states[component_name].status.value
    return "unknown"

async def run_degradation_monitoring():
    """Run degradation monitoring"""
    await degradation_manager.monitor_and_adjust()

if __name__ == "__main__":
    # Test degradation system
    print("🛡️ A.T.L.A.S. Graceful Degradation System")
    print("=" * 50)
    
    # Get system report
    report = degradation_manager.get_system_degradation_report()
    print(json.dumps(report, indent=2, default=str))
    
    # Test feature availability
    print(f"\nFeature 'ai_responses' available: {is_feature_available('ai_responses')}")
    print(f"Feature 'real_time_scanning' available: {is_feature_available('real_time_scanning')}")
    
    # Start monitoring if requested
    import sys
    if "--monitor" in sys.argv:
        asyncio.run(run_degradation_monitoring())
