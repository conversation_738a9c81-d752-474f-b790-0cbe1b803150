# A.T.L.A.S. Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# SECURITY WARNING: Never commit .env files with real API keys to version control
# =============================================================================

# ===== CORE API KEYS =====
# OpenAI API Key (Required for AI chat functionality)
OPENAI_API_KEY=your_openai_api_key_here

# Incite API Key (Optional - for advanced market data)
INCITE_API_KEY=your_incite_api_key_here

# Alpha Vantage API Key (Optional - for stock market data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# ===== LEGACY TRADING APIs =====
# Alpaca Trading API
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here

# Financial Modeling Prep API
FMP_API_KEY=your_fmp_api_key_here

# Predicto API (Optional)
PREDICTO_API_KEY=your_predicto_api_key_here

# Trading Configuration
PAPER_TRADING=true
DEBUG=true
ENVIRONMENT=development

# Validation Mode (set to true for testing without all API keys)
VALIDATION_MODE=false

# Enhanced Security Settings
LOG_LEVEL=INFO
PORT=8080

# Database Configuration
DATABASE_URL=sqlite:///atlas.db

# Performance Settings
API_TIMEOUT=30
CACHE_TTL=300
MAX_SCAN_RESULTS=50

# ML Model Configuration
ML_MODELS_ENABLED=true
ML_PREDICTION_CONFIDENCE_THRESHOLD=0.7

# Options Trading Configuration
OPTIONS_TRADING_ENABLED=true
OPTIONS_MAX_EXPIRY_DAYS=45
OPTIONS_MIN_VOLUME=100
OPTIONS_MAX_SPREAD_PERCENT=5.0

# Risk Management
DEFAULT_RISK_PERCENT=2.0
MAX_POSITIONS=10

# Proactive Assistant Configuration
PROACTIVE_ASSISTANT_ENABLED=true
MORNING_BRIEFING_TIME=09:00
ALERT_COOLDOWN_MINUTES=15
MIN_SIGNAL_STRENGTH=4

# Enhanced Memory System
ENHANCED_MEMORY_ENABLED=true
CONVERSATION_MEMORY_LIMIT=1000
MEMORY_IMPORTANCE_THRESHOLD=0.5

# Optional APIs (for enhanced functionality)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
BING_SEARCH_API_KEY=your_bing_search_api_key_here

# Social Media APIs (for sentiment analysis)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# ===== A.T.L.A.S. REORGANIZED SYSTEM CONFIGURATION =====
# Server Configuration for reorganized structure
ATLAS_HOST=localhost
ATLAS_PORT=8080
LEE_METHOD_API_PORT=5001

# Demo Mode (for testing without API keys)
ATLAS_DEMO_MODE=false

# Lee Method Scanner Configuration
LEE_METHOD_SCAN_INTERVAL=300
LEE_METHOD_MIN_HISTOGRAM_BARS=3
LEE_METHOD_MOMENTUM_THRESHOLD=0.1
LEE_METHOD_CONFIDENCE_THRESHOLD=0.7
LEE_METHOD_SYMBOLS=SPY,QQQ,IWM,AAPL,MSFT,GOOGL,AMZN,TSLA

# Database Path (reorganized structure)
ATLAS_DATABASE_PATH=databases/

# Error Recovery Settings
ERROR_RECOVERY_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=5
RETRY_MAX_ATTEMPTS=3

# Comprehensive Startup Settings
STARTUP_MODE=comprehensive
HEALTH_CHECK_INTERVAL=30

# Desktop Application Settings
DESKTOP_APP_ENABLED=true
DESKTOP_APP_PORT=8080

# Docker Configuration
DOCKER_ATLAS_HOST=0.0.0.0
DOCKER_ATLAS_PORT=8080
DOCKER_LEE_METHOD_PORT=5001
