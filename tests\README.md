# A.T.L.A.S. Tests

This folder contains test files for validating A.T.L.A.S. system functionality.

## Test Files:
- `test_lee_method_implementation.py` - Comprehensive Lee Method scanner validation

## Running Tests:

### Lee Method Test
```bash
cd tests
python test_lee_method_implementation.py
```

## Test Coverage:
- Lee Method pattern detection algorithms
- Real-time scanner functionality
- API integration testing
- Signal generation accuracy
- Multi-timeframe analysis validation

## Adding New Tests:
Place new test files in this directory following the naming convention `test_*.py`
